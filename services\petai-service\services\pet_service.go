package services

import (
	petai_po "eShop/domain/petai-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	petai_vo "eShop/view-model/petai-vo"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/spf13/cast"
)

// NewPetService 创建宠物服务
func NewPetService() *PetService {
	return &PetService{}
}

type PetService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.JwtInfo
}

// 宠物信息添加或更新
func (s *PetService) PetInfoAddOrUpdate(in petai_vo.PetInfo) (petInfoId string, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====新增或更新宠物,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	petBirthday, err := time.ParseInLocation(utils.DateTimeLayout, in.PetBirthday, time.Local)
	if err != nil {
		log.Error(logPrefix+"====解析宠物生日失败 err=", err.Error())
		err = errors.New("解析宠物生日失败")
		return
	}
	userPetInfo := petai_po.UserPetInfo{
		PetId:        "",
		UserInfoId:   in.UserInfoId,
		PetName:      in.PetName,
		PetAvatar:    in.PetAvatar,
		PetSex:       in.PetSex,
		PetKindof:    in.PetKindof,
		PetVariety:   in.PetVariety,
		PetBirthday:  petBirthday,
		PetFlower:    in.PetFlower,
		FlowerCode:   in.FlowerCode,
		PetNeutering: in.PetNeutering,
		PetStatus:    in.PetStatus,
	}
	if len(in.PetInfoId) <= 0 {
		userPetInfo.PetInfoId = utils.GenerateUUID()
		if _, err = session.Table("eshop.user_pet_info").Insert(&userPetInfo); err != nil {
			log.Error(logPrefix+"====新增宠物失败 err=", err.Error())
			err = errors.New("新增宠物失败")
			return
		}
		if err != nil {
			log.Error(logPrefix+"====新增或更新宠物失败 err=", err.Error())
			err = errors.New("新增宠物失败")
			return
		}
		petInfoId = userPetInfo.PetInfoId
	} else {
		_, err = session.Table("eshop.user_pet_info").
			Cols("pet_name,pet_avatar,pet_sex,pet_kindof,pet_variety,pet_birthday,pet_flower,flower_code,pet_neutering,pet_status").
			Where("pet_info_id = ?", in.PetInfoId).Update(&userPetInfo)
		if err != nil {
			log.Error(logPrefix+"====新增或更新宠物失败 err=", err.Error())
			err = errors.New("更新宠物失败")
			return
		}
	}

	return

}

// 编辑宠物状态
func (s *PetService) PetInfoEditStatus(in petai_vo.PetInfoEditStatusReq) (err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====编辑宠物状态,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	userPetInfo := petai_po.UserPetInfo{
		PetStatus: in.PetStatus,
	}
	_, err = session.Table("eshop.user_pet_info").
		Cols("pet_status").
		Where("pet_info_id = ?", in.PetInfoId).Update(&userPetInfo)
	if err != nil {
		log.Error(logPrefix+"====编辑宠物状态失败 err=", err.Error())
		return errors.New("编辑宠物状态失败")
	}

	return

}

// 获取用户宠物信息列表
func (s *PetService) PetInfoList(in petai_vo.PetInfoListReq) (out []*petai_vo.PetInfo, total int64, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("==== 获取用户宠物信息列表,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 100
	}
	// 只展示正常的宠物信息pet_status = 0

	sel := `a.id,
			a.pet_id ,
			a.pet_info_id,
			a.user_info_id ,
			a.pet_name,
			a.pet_sex,
			a.pet_kindof,
			a.pet_variety ,
			a.pet_neutering ,
			a.pet_vaccinated,
			a.pet_deworming ,
			a.pet_weight ,
			a.pet_long ,
			a.pet_height ,
			a.pet_source ,
			a.pet_status ,
			a.pet_avatar ,
			a.pet_birthday ,
			a.pet_homeday ,
			a.pet_remark ,
			a.create_time ,
			a.update_time ,
			a.face_id ,
			a.pet_code ,
			a.insurance_face_id ,
			a.dog_licence_code ,
			a.dog_vaccinate_code,
			a.pet_flower,
			a.flower_code,
			tspd2.pet_dict_name pet_variety_str`

	out = make([]*petai_vo.PetInfo, 0)
	session.Table("eshop.user_pet_info").Alias("a").Select(sel).
		Join("left", "scrm_organization_db.t_scrm_pet_dict tspd2", "a.pet_kindof = tspd2.pet_dict_parent_id  and a.pet_variety = tspd2.pet_dict_id").
		Where("a.pet_status = 0 ")
	if len(in.PetInfoId) > 0 {
		session.Where("a.pet_info_id =?", in.PetInfoId)
	}
	if len(in.UserInfoId) > 0 {
		session.Where("a.user_info_id =?", in.UserInfoId)
	}
	total, err = session.OrderBy("a.create_time desc").
		Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).FindAndCount(&out)
	if err != nil {
		log.Error(logPrefix+"==== 获取用户宠物信息列表失败 err=", err.Error())
		err = errors.New("获取用户宠物信息列表失败")
		return
	}

	for _, v := range out {
		v.AgeStr, v.AgeConversionStr = s.FormatAgeStr(v.PetBirthday, v.PetKindof)
		v.PetKindofStr = s.FormatKinOfName(v.PetKindof)
		v.PetSexStr = s.FormatSexName(v.PetSex)
		v.PetNeuteringStr = s.FormatNeutering(v.PetNeutering)
	}

	return

}

// 查询宠物的分类
func (s *PetService) FormatKinOfName(PetKindof int) (PetKindofStr string) {

	if PetKindof == 1000 {
		PetKindofStr = "猫"
	} else if PetKindof == 1001 {
		PetKindofStr = "犬"
	} else if PetKindof == 1002 {
		PetKindofStr = "其他"
	} else {
		PetKindofStr = "unKnown"
	}
	return

}

func (s *PetService) FormatNeutering(PetNeutering int) (PetNeuteringStr string) {

	if PetNeutering == 0 {
		PetNeuteringStr = "未绝育"
	} else if PetNeutering == 1 {
		PetNeuteringStr = "已绝育"
	} else if PetNeutering == -1 {
		PetNeuteringStr = "未知"
	}
	return

}

// 计算年龄

func (s *PetService) FormatSexName(petSex int) (petSexStr string) {

	if petSex == 1 {
		petSexStr = "公"
	} else if petSex == 2 {
		petSexStr = "母"
	} else {
		petSexStr = "未知"
	}
	return

}

// 获取自己医疗模型需要的宠物相关信息
func (s *PetService) PetInfoForMedical(PetInfoId string) (out *petai_vo.MedicalPetInfo, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	out = &petai_vo.MedicalPetInfo{}
	logPrefix := fmt.Sprintf("==== 获取自己医疗模型需要的宠物相关信息,入参:%s", PetInfoId)
	log.Info(logPrefix)

	petList, _, err := s.PetInfoList(petai_vo.PetInfoListReq{PageIndex: 1, PageSize: 1, PetInfoId: PetInfoId})
	if err != nil {
		log.Error(logPrefix+"==== 获取宠物信息失败 err=", err.Error())
		err = errors.New("获取宠物信息失败")
		return
	}
	if len(petList) == 0 {
		log.Info(logPrefix + "==== 宠物信息不存在")
		return
	}

	// 宠物体重 和 疫苗 和 驱虫信息 从宠物健康管理记录中获取
	latestRecordList, err := new(PetVaccinateService).GetPetVaccinateLatestList(PetInfoId)
	if err != nil {
		log.Error(logPrefix+"==== 获取宠物健康管理记录失败 err=", err.Error())
		err = errors.New("获取宠物健康管理记录失败")
		return
	}

	var PetWeight, LastestVaccinateTime, LastestVaccinateName, LastestDewormingTime, LastestDewormingName, Environment string
	for _, v := range latestRecordList {
		if v.RecordType == petai_po.UserPetVaccinateRecordTypeWeight {
			PetWeight = v.ProductName
		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeVaccinate {
			LastestVaccinateName = v.ProductName
			LastestVaccinateTime = v.OperationDate

		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeDeworming {
			LastestDewormingName = v.ProductName
			LastestDewormingTime = v.OperationDate
		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeEnvironment { //生活环境(多选)
			Environment = v.ProductName
		}

		/*else if v.RecordType == petai_po.UserPetVaccinateRecordTypeFeeding { //喂养方式(多选)  petai-v2.3.0 版本变化
		// 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重 101宠物病史记录表 102症状自诊 103报告解读 104(生活环境(多选) 喂养方式(多选) 喂食模式 洗澡频率 运动方式 运动时长) 105过敏史 106肌肉评分
			Feeding = v.ProductName
		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeWashFreq { //洗澡频率
			WashFreq = v.ProductName
		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeSport {
			MotionWayStr = v.ProductName
		} else if v.RecordType == petai_po.UserPetVaccinateRecordTypeSportTime {
			MotionDurationStr = v.ProductName
		}*/
	}

	//获取所有过敏源
	allergyStr, err := new(PetVaccinateService).GetPetAllergy(petai_vo.PetVaccinateListReq{Type: petai_po.UserPetVaccinateRecordTypeAllergy, PetInfoId: PetInfoId, PageIndex: 1, PageSize: 999})
	if err != nil {
		log.Errorf("%s 获取宠物过敏源失败", err.Error())
		err = errors.New("获取宠物过敏源失败")
		return
	}

	out = &petai_vo.MedicalPetInfo{
		PetName:              petList[0].PetName,
		PetKindofStr:         petList[0].PetKindofStr,            //s.FormatKinOfName(userPetInfo.PetKindof)    // 宠物种类
		PetVarietyStr:        petList[0].PetVarietyStr,           //s.FormatKinOfName(userPetInfo.PetVariety), // 宠物品种
		PetSexStr:            s.FormatSexName(petList[0].PetSex), // 宠物性别
		PetAge:               petList[0].AgeStr,                  // 宠物年龄
		PetWeight:            PetWeight,                          // 宠物体重(单位：kg)
		PetNeuteringStr:      petList[0].PetNeuteringStr,         // 宠物绝育
		LastestVaccinateTime: LastestVaccinateTime,               // 最近一次疫苗时间
		LastestVaccinateName: LastestVaccinateName,               // 最近一次疫苗名称
		LastestDewormingTime: LastestDewormingTime,               // 最近一次驱虫时间
		LastestDewormingName: LastestDewormingName,               // 最近一次驱虫名称
		Environment:          Environment,                        //生活环境(多选)
		//Feeding:              Feeding,                            //喂养方式(多选)
		//WashFreq:             WashFreq,                           // 洗澡频率
		AllergyStr: allergyStr, // 所有过敏源
	}
	//out.PetVarietyStr,
	out.FullText = fmt.Sprintf("宠物名称:%s,宠物种类:%s,宠物品种:%s,宠物性别:%s,宠物年龄:%s,是否绝育:%s,", out.PetName, out.PetKindofStr, out.PetVarietyStr, out.PetSexStr, out.PetAge, out.PetNeuteringStr)
	if out.LastestVaccinateName != "" {
		out.FullText = fmt.Sprintf("%s,最近一次疫苗名称:%s,最近一次疫苗时间:%s,", out.FullText, out.LastestVaccinateName, out.LastestVaccinateTime)
	}
	if out.LastestDewormingName != "" {
		out.FullText = fmt.Sprintf("%s,最近一次驱虫名称:%s,最近一次驱虫时间:%s,", out.FullText, out.LastestDewormingName, out.LastestDewormingTime)
	}

	if out.PetWeight != "" {
		out.FullText = fmt.Sprintf("%s,宠物体重:%skg,", out.FullText, out.PetWeight)
	}
	if out.Environment != "" {
		out.FullText = fmt.Sprintf("%s,生活环境:%s,", out.FullText, out.Environment)
	}
	/*if out.Feeding != "" {
		out.FullText = fmt.Sprintf("%s,喂养方式:%s,", out.FullText, out.Feeding)
	}
	if out.WashFreq != "" {
		out.FullText = fmt.Sprintf("%s,洗澡频率:%s,", out.FullText, out.WashFreq)
	}*/
	if out.AllergyStr != "" {
		out.FullText = fmt.Sprintf("%s,过敏源:%s", out.FullText, out.AllergyStr)
	}
	return
}

func (s *PetService) FormatAgeStr(petBirthday string, petKindof int) (ageStr, ageConversionStr string) {

	birthdayTime, _ := time.Parse(utils.DateTimeLayout, petBirthday)
	month, day, isWholeMonth := GetAge(time.Now(), birthdayTime)
	if petKindof == 1000 {
		ageStr, ageConversionStr = catAgeConvert(month, day, isWholeMonth)
	} else if petKindof == 1001 {
		ageStr, ageConversionStr = dogAgeConvert(month, day, isWholeMonth)
	} else {
		ageStr, _ = dogAgeConvert(month, day, isWholeMonth)
	}
	return

}

// GetAge 计算年龄 岁+月
func GetAge(t1, t2 time.Time) (month, day, isWholeMonth int) {
	y1 := t1.Year()
	y2 := t2.Year()
	m1 := int(t1.Month())
	m2 := int(t2.Month())
	d1 := t1.Day()
	d2 := t2.Day()

	// 0大于 1等于 2小于
	isWholeMonth = 0
	// 获取月数差值
	month = (y1-y2)*12 + (m1 - m2)
	day = d1 - d2

	if d1 < d2 {
		isWholeMonth = 2
		// 避免出现 1月-2天
		if month > 0 {
			month--
			// 生日当月天数
			day += time.Date(y2, t2.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 1, -1).Day()
		}
	} else if d1 == d2 {
		isWholeMonth = 1
	}

	return
}
func catAgeConvert(month, day, isWholeMonth int) (ageStr, ageConvertStr string) {
	if month == 0 {
		ageStr = ageStr + cast.ToString(day) + "天"
	} else {
		if (month / 12) > 0 {
			ageStr = ageStr + cast.ToString(month/12) + "岁"
		}
		if (month % 12) > 0 {
			ageStr = ageStr + cast.ToString(month%12) + "个月"
		}
	}

	age := 0
	if month < 1 {
		age = 1

	} else if month >= 1 && month < 2 {
		age = 3
		if month == 1 && isWholeMonth == 1 {
			age = 1
		}
	} else if month >= 2 && month < 3 {
		age = 5
		if month == 2 && isWholeMonth == 1 {
			age = 3
		}
	} else if month >= 3 && month < 6 {
		age = 9
		if month == 3 && isWholeMonth == 1 {
			age = 5
		}
	} else if month >= 6 && month < 9 {
		age = 12
		if month == 6 && isWholeMonth == 1 {
			age = 9
		}
	} else if month >= 9 && month < 12 {
		age = 17
		if month == 6 && isWholeMonth == 1 {
			age = 12
		}
	} else if month >= 12 && month < 24 {
		age = 24
		if month == 12 && isWholeMonth == 1 {
			age = 17
		}
	} else if month >= 24 && month < 36 {
		age = 28
		if month == 24 && isWholeMonth == 1 {
			age = 24
		}
	} else if month >= 36 && month < 48 {
		age = 32
		if month == 36 && isWholeMonth == 1 {
			age = 28
		}
	} else if month >= 48 && month < 60 {
		age = 36
		if month == 48 && isWholeMonth == 1 {
			age = 32
		}
	} else if month >= 60 && month < 72 {
		age = 40
		if month == 60 && isWholeMonth == 1 {
			age = 36
		}
	} else if month >= 72 && month < 84 {
		age = 44
		if month == 72 && isWholeMonth == 1 {
			age = 40
		}
	} else if month >= 84 && month < 96 {
		age = 48
		if month == 84 && isWholeMonth == 1 {
			age = 44
		}
	} else if month >= 96 && month < 108 {
		age = 52
		if month == 96 && isWholeMonth == 1 {
			age = 48
		}
	} else if month >= 108 && month < 120 {
		age = 56
		if month == 108 && isWholeMonth == 1 {
			age = 52
		}
	} else if month >= 120 && month < 132 {
		age = 60
		if month == 120 && isWholeMonth == 1 {
			age = 56
		}
	} else if month >= 132 && month < 144 {
		age = 64
		if month == 132 && isWholeMonth == 1 {
			age = 60
		}
	} else if month >= 144 && month < 156 {
		age = 68
		if month == 144 && isWholeMonth == 1 {
			age = 64
		}
	} else if month >= 156 && month < 168 {
		age = 72
		if month == 156 && isWholeMonth == 1 {
			age = 68
		}
	} else if month >= 168 && month < 180 {
		age = 76
		if month == 168 && isWholeMonth == 1 {
			age = 72
		}
	} else if month >= 180 {
		age = 80
		if month == 180 && isWholeMonth == 1 {
			age = 76
		}
	}
	ageConvertStr = cast.ToString(age) + "岁"
	return
}
func dogAgeConvert(month, day, isWholeMonth int) (ageStr, ageConvertStr string) {
	if month == 0 {
		ageStr = ageStr + cast.ToString(day) + "天"
	} else {
		if (month / 12) > 0 {
			ageStr = ageStr + cast.ToString(month/12) + "岁"
		}
		if (month % 12) > 0 {
			ageStr = ageStr + cast.ToString(month%12) + "个月"
		}
	}

	age := 0
	if month < 2 {
		age = 2

	} else if month >= 2 && month < 4 {
		age = 6
		if month == 2 && isWholeMonth == 1 {
			age = 2
		}
	} else if month >= 4 && month < 6 {
		age = 10
		if month == 4 && isWholeMonth == 1 {
			age = 6
		}
	} else if month >= 6 && month < 8 {
		age = 12
		if month == 6 && isWholeMonth == 1 {
			age = 10
		}
	} else if month >= 8 && month < 10 {
		age = 14
		if month == 8 && isWholeMonth == 1 {
			age = 12
		}
	} else if month >= 10 && month < 12 {
		age = 16
		if month == 10 && isWholeMonth == 1 {
			age = 14
		}
	} else if month >= 12 && month < 18 {
		age = 20
		if month == 12 && isWholeMonth == 1 {
			age = 16
		}
	} else if month >= 18 && month < 24 {
		age = 24
		if month == 18 && isWholeMonth == 1 {
			age = 20
		}
	} else if month >= 24 && month < 36 {
		age = 29
		if month == 24 && isWholeMonth == 1 {
			age = 24
		}
	} else if month >= 36 && month < 48 {
		age = 34
		if month == 36 && isWholeMonth == 1 {
			age = 29
		}
	} else if month >= 48 && month < 60 {
		age = 37
		if month == 48 && isWholeMonth == 1 {
			age = 34
		}
	} else if month >= 60 && month < 72 {
		age = 42
		if month == 60 && isWholeMonth == 1 {
			age = 37
		}
	} else if month >= 72 && month < 84 {
		age = 47
		if month == 72 && isWholeMonth == 1 {
			age = 42
		}
	} else if month >= 84 && month < 96 {
		age = 51
		if month == 84 && isWholeMonth == 1 {
			age = 47
		}
	} else if month >= 96 && month < 108 {
		age = 56
		if month == 96 && isWholeMonth == 1 {
			age = 51
		}
	} else if month >= 108 && month < 120 {
		age = 60
		if month == 108 && isWholeMonth == 1 {
			age = 56
		}
	} else if month >= 120 && month < 132 {
		age = 65
		if month == 120 && isWholeMonth == 1 {
			age = 60
		}
	} else if month >= 132 && month < 144 {
		age = 69
		if month == 132 && isWholeMonth == 1 {
			age = 65
		}
	} else if month >= 144 && month < 156 {
		age = 74
		if month == 144 && isWholeMonth == 1 {
			age = 69
		}
	} else if month >= 156 && month < 168 {
		age = 78
		if month == 156 && isWholeMonth == 1 {
			age = 74
		}
	} else if month >= 168 && month < 180 {
		age = 83
		if month == 168 && isWholeMonth == 1 {
			age = 78
		}
	} else if month >= 180 {
		age = 87
		if month == 180 && isWholeMonth == 1 {
			age = 83
		}
	}
	ageConvertStr = cast.ToString(age) + "岁"
	return
}
