package petai_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

const (
	BotTypePetCareAssistant  = 1 //养宠助手火山
	BotTypeXiaoWenModel      = 2 //小闻模型
	BotTypePetDiagnose       = 3 //宠物自诊
	BotTypePetRecog          = 4 //宠物识别
	BotTypeHealthType        = 5 //健康建议
	BotTypePetMedical        = 6 //互联网医院
	BotTypeQuestionRecommend = 7 //coze问题推荐
)

// 小闻养宠助手会话表
type PetaiConversation struct {
	Id         int    `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	UserInfoId string `json:"user_info_id"`                              //eshop.user_info.user_info_id用户ID作为唯一标示
	PetInfoId  string `json:"pet_info_id"`                               //eshop.user_pet_info.pet_info_id宠物ID作为唯一标示
	PetName    string `json:"pet_name"`                                  //宠物名称
	PetKindof  int    `json:"pet_kindof" xorm:"not null TINYINT(4)"`     // 宠物品种 -1未知
	PetVariety int    `json:"pet_variety" xorm:"not null TINYINT(4)"`    // 宠物品种 -1未知
	BotId      string `json:"bot_id"`                                    // 智能体id
	BotType    int    `json:"bot_type" xorm:"not null TINYINT(4)"`       // 智能体类型：1-养22宠助手火山 2-小闻模型 3-宠物自诊 4-宠物识别；5-健康建议 6-互联网医院
	// CozeConversationId   string    `json:"coze_conversation_id"`                      //coze中的会话id
	// ConversationCreateAt int       `json:"conversation_create_at"`                    //coze会话创建时间
	Title      string    `json:"title"`                      //会话标题
	IsDeleted  int       `json:"is_deleted"`                 //消息是否被删除，0否，1是
	CreateTime time.Time `json:"create_time" xorm:"created"` //创建时间
	UpdateTime time.Time `json:"update_time" xorm:"updated"` //更新时间

}

// 今天、周、月
type PetaiConversationExd struct {
	PetaiConversation `xorm:"extends"`
	UserMobile        string `json:"user_mobile" xorm:"<-"` //用户手机号
}

func (m *PetaiConversation) TableName() string {
	return "eshop.petai_conversation"
}

// 根据会话id获取会话信息
func (m *PetaiConversation) GetConversationByConversationId(session *xorm.Session, conversationId int) (out PetaiConversation, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if conversationId == 0 {
		err = errors.New("会话id为空")
		return
	}

	session.Table("eshop.petai_conversation").Alias("a").
		Where("a.id=?", conversationId)

	exists, err := session.Get(&out)
	if err != nil {
		return
	}
	if !exists {
		err = errors.New("会话不存在")
		return
	}
	return
}

// 根据会话id获取会话信息
func (m *PetaiConversation) GetConversationByConversationId2(session *xorm.Session, conversationId int) (out PetaiConversationExd, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if conversationId == 0 {
		err = errors.New("会话id为空")
		return
	}

	session.Select("a.*,b.user_mobile").Table("eshop.petai_conversation").Alias("a").
		Join("inner", "eshop.user_info b", "a.user_info_id=b.user_info_id").
		Where("a.id=?", conversationId)

	exists, err := session.Get(&out)
	if err != nil {
		return
	}
	if !exists {
		err = errors.New("会话不存在")
		return
	}
	return
}

// 创建会话
func (m *PetaiConversation) Create(session *xorm.Session) (err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	_, err = session.Insert(m)
	return
}

// 更新会话
func (m *PetaiConversation) Update(session *xorm.Session) (err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	_, err = session.Cols("bot_id,bot_type").Where("id=?", m.Id).Update(m)
	return
}

// DelChatByConversationId 根据会话id删除会话
func (m *PetaiConversation) DelChatByConversationId(session *xorm.Session, ids []int, userInfoId string) error {
	if session == nil {
		return errors.New("session is nil")
	}
	if len(ids) == 0 {
		return errors.New("会话id为空")
	}

	_, err := session.
		Table(new(PetaiConversation)).
		Where("user_info_id=?", userInfoId).
		And("is_deleted = 0").
		In("id", ids). // 使用In方法进行批量操作
		Update(map[string]interface{}{"is_deleted": 1})
	return err
}
