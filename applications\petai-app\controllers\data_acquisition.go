package controllers

import (
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/petai-service/services"
	petai_vo "eShop/view-model/petai-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

func NewDataAcquisitionController(service services.DataAcquisitionService) *DataAcquisitionController {
	return &DataAcquisitionController{
		service: service,
	}
}

// 宠物病史记录之 驱虫、免疫
type DataAcquisitionController struct {
	service services.DataAcquisitionService
}

func (w *DataAcquisitionController) RegisterRoutes(r chi.Router) {

	//小程序
	r.Route("/petai-app/api/data-acquisition", func(r chi.Router) {
		r.Post("/add", w.DataAcquisitionAdd)   // 数据采集新增数据
		r.Post("/list", w.DataAcquisitionList) // 数据采集列表(根据消息uuid，获取采集数据列表)
		r.Post("/edit", w.DataAcquisitionEdit) // 数据采集编辑

	})

}

// DataAcquisitionAdd 数据采集新增数据
// @Summary 数据采集新增数据 @petai-v1.1.0 @petai-v2.2.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.DataAcquisitionAddReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/data-acquisition/add [post]
func (c *DataAcquisitionController) DataAcquisitionAdd(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.DataAcquisitionAddReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	if cmd.QuestionUuid == "" {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	err = c.service.DataAcquisitionAdd(cmd)
	if err != nil {
		log.Error("====数据采集新增数据====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}


// DataAcquisitionEdit 编辑数据采集数据
// @Summary 编辑数据采集数据 @petai-v1.1.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.DataAcquisitionListRes true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/data-acquisition/edit [post]
func (c *DataAcquisitionController) DataAcquisitionEdit(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.DataAcquisitionEditReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	if len(cmd.Data) == 0 {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	err = c.service.DataAcquisitionEdit(cmd)
	if err != nil {
		log.Error("====数据采集编辑数据====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// DataAcquisitionList 数据采集列表
// @Summary 数据采集列表 @petai-v1.1.0
// @Description
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.DataAcquisitionListReq true "请求对话参数"
// @Success 200 {object} petai_vo.DataAcquisitionListRes "成功"
// @Failure 400 {object} petai_vo.DataAcquisitionListRes "请求错误"
// @Router /petai-app/api/data-acquisition/list [post]
func (c *DataAcquisitionController) DataAcquisitionList(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[petai_vo.DataAcquisitionListReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	if cmd.ConversationId == 0 {
		return
	}

	data, err := c.service.DataAcquisitionList(cmd)
	if err != nil {
		log.Error("====数据采集列表====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, data)
}
