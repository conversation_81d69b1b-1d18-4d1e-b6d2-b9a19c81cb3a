package services

import (
	"context"
	petai_po "eShop/domain/petai-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	petai_vo "eShop/view-model/petai-vo"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/coze-dev/coze-go"
	"github.com/spf13/cast"
)

type SymptomInfo struct {
	BathFreq     string   `json:"bath_freq"`
	FeedWay      string   `json:"feed_way"`
	SymptomType  string   `json:"symptom_type"`
	SymptomDesc  string   `json:"symptom_desc"`
	SymptomImage []string `json:"symptom_image"`
}

// DealPetInfoAndSymptomInfo   处理宠物信息
func DealPetInfoAndSymptomInfo(pmConsultInfo *petai_po.PmConsultInfo) (petInfoStr string, symptomStr string, symptomInfo SymptomInfo, petInfoDetail *petai_vo.PetInfoDetail) {
	petInfoStr = "宠物信息："

	//小可爱 母/猫/布偶猫/2岁8个月/已免疫/未绝育
	petSex := ""
	if pmConsultInfo.PetSex == 2 {
		petSex = "母"
	} else if pmConsultInfo.PetSex == 1 {
		petSex = "公"
	}
	immuneStr := ""
	//1已免疫，2未免疫，3免疫不全，4免疫不详
	switch pmConsultInfo.ImmuneStatus {
	case 1:
		immuneStr = "已免疫"
	case 2:
		immuneStr = "未免疫"
	case 3:
		immuneStr = "免疫不全"
	case 4:
		immuneStr = "免疫不详"
	}
	petNeuteringStr := ""
	if pmConsultInfo.PetNeutering == 1 {
		petNeuteringStr = "已绝育"
	} else if pmConsultInfo.PetNeutering == 2 {
		petNeuteringStr = "未绝育"
	}

	petWeight := pmConsultInfo.PetWeight
	if len(pmConsultInfo.PetWeight) > 0 {
		if petWeight != "体重未知" && petWeight != "体重" {
			petWeight = petWeight + "kg"
		}
	}

	petInfoStr += pmConsultInfo.PetName + " "
	if petSex != "" {
		petInfoStr += petSex
	}
	if pmConsultInfo.PetKindof != "" {
		petInfoStr += "/" + pmConsultInfo.PetKindof
	}
	if pmConsultInfo.PetVariety != "" {
		petInfoStr += "/" + pmConsultInfo.PetVariety
	}
	if pmConsultInfo.PetAge != "" {
		petInfoStr += "/" + pmConsultInfo.PetAge
	}
	if petWeight != "" {
		petInfoStr += "/" + petWeight
	}
	if immuneStr != "" {
		petInfoStr += "/" + immuneStr
	}
	if petNeuteringStr != "" {
		petInfoStr += "/" + petNeuteringStr
	}

	BathFreqMap := make(map[int32]string, 0)
	BathFreqMap[1] = "一周一次"
	BathFreqMap[2] = "洗澡频次不规律"
	BathFreqMap[3] = "想起来才洗"
	BathFreqMap[4] = "定期洗澡"
	if bathFreq, ok := BathFreqMap[pmConsultInfo.BathFreq]; ok {
		symptomInfo.BathFreq = bathFreq
		petInfoStr += "/" + bathFreq
	}
	//1-配方粮,2-配方粮+零食,3-只吃人的食物,4-自制犬猫粮
	FeedWayMap := make(map[int32]string, 0)
	FeedWayMap[1] = "配方粮"
	FeedWayMap[2] = "配方粮+零食"
	FeedWayMap[3] = "只吃人的食物"
	FeedWayMap[4] = "自制犬猫粮"
	if feedWay, ok := FeedWayMap[pmConsultInfo.FeedWay]; ok {
		symptomInfo.FeedWay = feedWay
		petInfoStr += "/" + feedWay
	}
	//10其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题
	SymptomTypeMap := make(map[int32]string, 0)
	SymptomTypeMap[1] = "呕吐"
	SymptomTypeMap[2] = "软便拉稀"
	SymptomTypeMap[3] = "皮肤问题"
	SymptomTypeMap[4] = "眼睛问题"
	SymptomTypeMap[5] = "泌尿问题"
	SymptomTypeMap[6] = "绝育"
	SymptomTypeMap[7] = "疫苗"
	SymptomTypeMap[8] = "驱虫"
	SymptomTypeMap[9] = "养护问题"
	SymptomTypeMap[10] = "其他"
	symptomTypeArray := []string{}
	if pmConsultInfo.SymptomType != "" {
		symptomTypeArray = strings.Split(pmConsultInfo.SymptomType, ",")
	}
	symptomTypeStrArray := []string{}
	for _, v := range symptomTypeArray {
		_symptomType, _ := strconv.Atoi(v)
		if symptomType, ok := SymptomTypeMap[int32(_symptomType)]; ok {
			symptomTypeStrArray = append(symptomTypeStrArray, symptomType)
		}
	}
	symptomInfo.SymptomType = strings.Join(symptomTypeStrArray, ",")
	if pmConsultInfo.SymptomImage != "" {
		symptomInfo.SymptomImage = strings.Split(pmConsultInfo.SymptomImage, ",")
	}

	SymptomTimeMap := make(map[int32]string, 0)
	SymptomTimeMap[1] = "小于7天"
	SymptomTimeMap[2] = "小于1个月"
	SymptomTimeMap[3] = "小于3个月"
	SymptomTimeMap[4] = "3个月以上"

	if symptomTime, ok := SymptomTimeMap[pmConsultInfo.SymptomRecent]; ok {
		if pmConsultInfo.Symptom != "" {
			symptomInfo.SymptomDesc = fmt.Sprintf("症状出现周期：%s；具体描述：%s", symptomTime, pmConsultInfo.Symptom)
		} else {
			symptomInfo.SymptomDesc = fmt.Sprintf("症状出现周期：%s", symptomTime)
		}
	} else {
		symptomInfo.SymptomDesc = pmConsultInfo.Symptom
	}

	if symptomTime, ok := SymptomTimeMap[pmConsultInfo.SymptomRecent]; ok {
		symptomStr += "症状出现周期：" + symptomTime + "；"
	}
	if symptomInfo.SymptomType != "" || pmConsultInfo.Symptom != "" {
		symptomStr += "具体描述："
	}
	if symptomInfo.SymptomType != "" {
		symptomStr += symptomInfo.SymptomType + ";"
	}
	if pmConsultInfo.Symptom != "" {

		if strings.Contains(pmConsultInfo.Symptom, "【补充描述】：") {
			if len(string([]rune(pmConsultInfo.Symptom)[7:])) > 0 {
				symptomStr += string([]rune(pmConsultInfo.Symptom)[7:])
			}
		} else {
			symptomStr += pmConsultInfo.Symptom
		}
	}
	if len(symptomStr) > 0 {
		symptomStr = "症状描述：" + symptomStr
	}
	petInfoDetail = &petai_vo.PetInfoDetail{
		PetId:           pmConsultInfo.PetId,
		PetName:         pmConsultInfo.PetName,
		PetKindofStr:    pmConsultInfo.PetKindof,
		PetVarietyStr:   pmConsultInfo.PetVariety,
		PetSexStr:       petSex,
		PetAge:          pmConsultInfo.PetAge,
		PetWeight:       petWeight,
		PetNeuteringStr: petNeuteringStr,
	}
	return petInfoStr, symptomStr, symptomInfo, petInfoDetail
}

// 互联网医院问诊单 创建一个对应的会话
func (s *CozeService) ConsultConversationCreate(in petai_vo.ConsultConversationCreateReq) (consultConversation *petai_po.PetaiConsultConversation, petInfoDetail *petai_vo.PetInfoDetail, isNew bool, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====创建互联网医院问诊单会话====问诊单号=%d", in.PmOrderSn)
	log.Info(logPrefix, "入参:", utils.JsonEncode(in))

	// 根据问诊单号 查询问诊单详情
	pmConsultInfo, err := new(petai_po.PmConsultInfo).GetConsultInfoByrderSn(session, in.PmOrderSn)
	if err != nil {
		log.Error(logPrefix, "获取问诊单详情失败:", err.Error())
		err = errors.New("获取问诊单详情失败")
		return
	}

	if pmConsultInfo == nil || pmConsultInfo.Id <= 0 {
		log.Error(logPrefix, "问诊单不存在")
		err = errors.New("问诊单不存在")
		return
	}
	// 组装宠物信息和症状描述
	petInfoStr, symptomStr, _, petInfoDetail := DealPetInfoAndSymptomInfo(pmConsultInfo)

	// 查询问诊会话id是否存在， 若不存在， 则创建一个问诊会话
	if pmConsultInfo.ConsultConversationId <= 0 {
		// 如果是新创建问诊会话， 这时发起第一次对话
		isNew = true
		// 创建一个问诊会话

		consultConversation = &petai_po.PetaiConsultConversation{
			PmOrderSn:  in.PmOrderSn,
			ScrmUserId: pmConsultInfo.MemberId,
			DoctorCode: pmConsultInfo.DoctorCode,
			Title:      petInfoStr + ";" + symptomStr,
		}
		session.Begin()
		defer session.Close()
		_, err = session.Table("eshop.petai_consult_conversation").Insert(consultConversation)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "创建问诊会话失败:", err.Error())
			err = errors.New("创建问诊会话失败")
			return
		}

		// 更新互联网医院pet_medical.pm_consult_info表的consult_conversation_id字段
		_, err = session.Table("pet_medical.pm_consult_info").Where("order_sn = ?", pmConsultInfo.OrderSn).Update(map[string]interface{}{
			"consult_conversation_id": consultConversation.Id,
		})
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新互联网医院pet_medical.pm_consult_info表的consult_conversation_id字段失败:", err.Error())
			err = errors.New("更新互联网医院pet_medical.pm_consult_info表的consult_conversation_id字段失败")
			return
		}
		session.Commit()
	} else {
		//   `consult_status` int NOT NULL DEFAULT '1' COMMENT '咨询状态：1、无状态（对应下了付费订单，但还没付款时候的状态），2、待接入，3、待回复，4、问诊中，5、已完成，6、已取消',

		// if pmConsultInfo.ConsultStatus != 3 && pmConsultInfo.ConsultStatus != 4 {
		// 	err = errors.New("该问诊单不是待回复或问诊中状态，不能发起对话")
		// 	return
		// }
		consultConversation, err = new(petai_po.PetaiConsultConversation).GetConsultConversationByPmOrderSn(session, in.PmOrderSn)
		if err != nil {
			log.Error(logPrefix, "获取问诊会话失败:", err.Error())
			err = errors.New("获取问诊会话失败")
			return
		}
		if consultConversation == nil || consultConversation.Id <= 0 {
			log.Error(logPrefix, "问诊会话不存在")
			err = errors.New("问诊会话不存在")
			return
		}
	}
	return
}

// 创建会话
func (s *CozeService) ConversationCreate(in petai_vo.ConversationCreateReq) (conversation *petai_po.PetaiConversation, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====创建coze会话====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	if len(in.PetInfoId) > 0 && len(in.PetName) == 0 {
		err = errors.New("宠物名称不能为空")
		return
	}

	if s.NeedSafeCheck() && !utils.CheckContent(utils.CheckContentReq{Content: in.Title, Type: 1}) {
		err = errors.New("内容含违规信息，请修改后重新提交")
		return
	}

	// ctx := context.Background()
	// // Init the Coze client through the access_token.
	// cozeCli, cozeConfig := s.NewCozeAPI(in.UserInfoId)
	// // Create a new conversation
	// resp, err := cozeCli.Conversations.Create(ctx, &coze.CreateConversationsReq{BotID: cozeConfig.BotId})
	// log.Infof("%s resp:%s|err:%v", logPrefix, utils.JsonEncode(resp), err)
	// if err != nil {
	// 	log.Error(logPrefix, "Error creating conversation:", err.Error())
	// 	err = errors.New("创建会话失败")
	// 	return
	// }
	// if resp == nil || len(resp.Conversation.ID) == 0 {
	// 	log.Error(logPrefix, "Error creating conversation: resp.Conversation is nil")
	// 	err = errors.New("创建会话失败.")
	// 	return
	// }

	conversation = &petai_po.PetaiConversation{
		UserInfoId: in.UserInfoId,
		PetInfoId:  in.PetInfoId,
		PetKindof:  in.PetKindof,
		PetVariety: in.PetVariety,
		PetName:    in.PetName,
		// BotId:                botId,
		// BotType:              in.BotType,
		// CozeConversationId:   resp.Conversation.ID,
		// ConversationCreateAt: resp.Conversation.CreatedAt,
		Title: in.Title,
	}
	log.Info(logPrefix, "创建本地会话数据", utils.JsonEncode(conversation))
	if err = conversation.Create(session); err != nil {
		log.Error(logPrefix, "Error creating conversation: ", err.Error())
		err = errors.New("创建会话失败..")
		return
	}

	return
}

// 获取会话详情
func (s *CozeService) ConversationDetail(in petai_vo.ConversationDetailReq) (conversation *petai_po.PetaiConversation, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====获取coze会话详情====会话id=%d入参:%s", in.ConversationId, utils.JsonEncode(in))
	log.Info(logPrefix)
	var petaiConversationInfo petai_po.PetaiConversation
	if in.ConversationId > 0 {
		// 获取coze会话id
		petaiConversationInfo, err = new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.ConversationId)
		if err != nil {
			log.Error(logPrefix, "获取会话信息失败:", err.Error())
			err = errors.New("获取会话信息失败")
			return
		}
		if petaiConversationInfo.Id == 0 {
			err = errors.New("会话信息不存在")
			return
		}
	}

	log.Info(logPrefix, "获取本地会话信息成功", utils.JsonEncode(petaiConversationInfo))

	// if in.IsCozeData {
	// 	ctx := context.Background()
	// 	// Init the Coze client through the access_token.
	// 	cozeCli, _ := s.NewCozeAPI(in.UserInfoId)
	// 	// 从 Coze API 获取最新会话信息
	// 	resp, e := cozeCli.Conversations.Retrieve(ctx, &coze.RetrieveConversationsReq{
	// 		ConversationID: petaiConversationInfo.CozeConversationId,
	// 	})
	// 	log.Infof("%s resp:%s|err:%v", logPrefix, utils.JsonEncode(resp), e)
	// 	if e != nil {
	// 		log.Error(logPrefix, "获取coze会话详情失败:", err.Error())
	// 		err = errors.New("获取会话详情失败")
	// 		return
	// 	}

	// 	if resp == nil || len(resp.Conversation.ID) == 0 {
	// 		log.Error(logPrefix, "获取coze会话详情失败: resp.Conversation is nil")
	// 		err = errors.New("获取会话详情失败")
	// 		return
	// 	}
	// 	conversation = &petai_po.PetaiConversation{
	// 		UserInfoId: in.UserInfoId,
	// 		PetInfoId:  "",
	// 		BotId:      "",
	// 		// 会话信息
	// 		CozeConversationId:   resp.Conversation.ID,
	// 		ConversationCreateAt: resp.Conversation.CreatedAt,
	// 	}
	// 	return
	// }

	conversation = &petaiConversationInfo

	return
}

func (s *CozeService) ConversationMapCoze(conversationId int) (out map[int]petai_po.PetaiConversationCoze, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====获取会话对应的coze会话====会话id=%d", conversationId)
	log.Info(logPrefix)

	return new(petai_po.PetaiConversationCoze).GetMap(session, conversationId)
}

func (s *CozeService) CreateConversationCoze(in *petai_po.PetaiConversationCoze) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====创建会话对应的coze会话====入参=%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	_, err = session.Table("eshop.petai_conversation_coze").Insert(in)

	return
}

func (s *CozeService) EditConversation(in *petai_po.PetaiConversation) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====编辑会话====会话id=%d,入参:%s", in.Id, utils.JsonEncode(in))
	log.Info(logPrefix)

	// 只有会话id和botId不为空时才更新
	if in != nil && in.Id > 0 {
		info, e := new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.Id)
		if e != nil {
			log.Error(logPrefix, "获取会话信息失败:", e.Error())
			err = errors.New("获取会话信息失败")
			return
		}
		if info.BotId == "" {
			_, err = session.Cols("bot_id,bot_type").Where("id=?", in.Id).Update(in)
		}

	}
	return
}

func (s *CozeService) ConversationDetailForAdmin(in petai_vo.ConversationDetailForAdminReq) (out petai_vo.ConversationDetailForAdmin, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====管理后台获取会话详情====会话id=%d,入参:%s", in.ConversationId, utils.JsonEncode(in))
	log.Info(logPrefix)

	// 获取coze会话id
	out.ConversationDetail, err = new(petai_po.PetaiConversation).GetConversationByConversationId2(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话信息失败:", err.Error())
		err = errors.New("获取会话信息失败")
		return
	}
	if out.ConversationDetail.Id == 0 {
		err = errors.New("会话信息不存在")
		return
	}
	log.Info(logPrefix, "获取本地会话信息成功", utils.JsonEncode(out.ConversationDetail))

	// 获取转接互联网人工
	out.ConversationTransfer, err = new(petai_po.PetaiConversationTransfer).GetConversationTransfers(session, petai_po.GetConversationTransfersReq{ConversationId: in.ConversationId})
	if err != nil {
		log.Error(logPrefix, "获取会话转接互联网人工列表失败:", err.Error())
		err = errors.New("获取会话转接互联网人工列表失败")
		return
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 9999
	}
	out.MessageList = make([]petai_po.PetaiMessage, 0)
	// 获取会话消息列表
	t, e := session.Table("eshop.petai_message").Alias("a").
		Where("a.conversation_id = ?", in.ConversationId).
		Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).
		OrderBy("a.message_time asc,a.id asc").FindAndCount(&out.MessageList)
	if e != nil {
		log.Error(logPrefix, "获取会话消息列表失败:", err.Error())
		err = errors.New("获取会话消息列表失败")
		return
	}
	total = int(t)

	return
}

// 获取互联网问诊单对应的会话消息列表
func (s *CozeService) ConsultConversationMessageList(in petai_vo.ConsultConversationMessageListReq) (messages []*petai_po.PetaiConsultMessage, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====获取会话消息列表ConsultConversationMessageList====会话id=%d,入参:%s", in.ConversationId, utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.PageSize == 0 {
		in.PageSize = 10
	}
	if in.PageIndex == 0 {
		in.PageIndex = 1
	}
	messages = make([]*petai_po.PetaiConsultMessage, 0)

	// 获取coze会话id
	petaiConversationInfo, err := new(petai_po.PetaiConsultConversation).GetConsultConversationById(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话信息失败:", err.Error())
		err = errors.New("获取会话信息失败")
		return
	}
	if petaiConversationInfo == nil || petaiConversationInfo.Id == 0 {
		err = errors.New("会话信息不存在")
		return
	}

	session.Table("eshop.petai_consult_message").Alias("a").
		Where("a.consult_conversation_id = ?", in.ConversationId)

	t, e := session.Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).
		OrderBy("a.id asc").FindAndCount(&messages)
	if e != nil {
		log.Error(logPrefix, "获取会话消息列表失败:", err.Error())
		err = errors.New("获取会话消息列表失败")
		return
	}
	total = int(t)

	return
}

// 获取会话消息列表
func (s *CozeService) ConversationMessageList(in petai_vo.ConversationMessageListReq) (messages []*petai_po.PetaiMessage, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====获取会话消息列表====会话id=%d,入参:%s", in.ConversationId, utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.PageSize == 0 {
		in.PageSize = 10
	}
	messages = make([]*petai_po.PetaiMessage, 0)

	// 获取coze会话id
	petaiConversationInfo, err := new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话信息失败:", err.Error())
		err = errors.New("获取会话信息失败")
		return
	}
	if petaiConversationInfo.Id == 0 {
		err = errors.New("会话信息不存在")
		return
	}

	// 获取coze会话列表
	cozeConversationList, err := new(petai_po.PetaiConversationCoze).GetMap(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话对应coze会话信息失败:", err.Error())
		err = errors.New("获取会话对应coze会话信息失败")
		return
	}

	if len(cozeConversationList) > 0 && in.IsCozeData {
		for _, v := range cozeConversationList {
			cozeCli, _ := s.NewCozeAPI(in.UserInfoId, v.BotType)
			ctx := context.Background()

			// you can use iterator to automatically retrieve next page
			message, e := cozeCli.Conversations.Messages.List(ctx, &coze.ListConversationsMessagesReq{Limit: in.PageSize, ConversationID: v.CozeConversationId})
			if e != nil {
				log.Error(logPrefix, "获取会话消息列表失败:", e.Error())
				err = errors.New("获取会话消息列表失败")
				return
			}

			for message.Next() {
				createTime, _ := time.ParseInLocation(utils.DateTimeLayout, cast.ToString(message.Current().CreatedAt), time.Local)
				messages = append(messages, &petai_po.PetaiMessage{
					UserInfoId:               in.UserInfoId,
					BotId:                    s.GetBotId(v.BotType),
					BotType:                  v.BotType,
					CozeConversationId:       v.CozeConversationId,
					CozeConversationCreateAt: v.CozeConversationCreateAt,
					ConversationId:           in.ConversationId,
					SectionId:                message.Current().SectionID,
					ChatId:                   message.Current().ChatID,
					CozeMessageId:            message.Current().ID,
					ReasoningContent:         message.Current().ReasoningContent,
					Role:                     string(message.Current().Role),
					Type:                     string(message.Current().Type),
					ContentType:              string(message.Current().ContentType),
					Content:                  utils.JsonEncode(message.Current().Content),
					MetaData:                 utils.JsonEncode(message.Current().MetaData),
					CreateTime:               createTime,
				})

			}
			if message.Err() != nil {
				log.Error(logPrefix, "Error fetching message:", message.Err())
				err = errors.New("获取会话消息列表失败")
				return
			}
		}

	} else {
		if in.OnlyLastSection && in.SectionUuid == "" {
			in.SectionUuid, _, err = new(petai_po.PetaiMessage).GetLastestSectionUuid(session, in.ConversationId)
		}
		session.Table("eshop.petai_message").Alias("a").Omit("a.bot_id").
			Where("a.conversation_id = ?", in.ConversationId)
		if in.DataFrom > 0 {
			session.Where("a.data_from =?", in.DataFrom)
		}
		if in.SectionUuid != "" {
			session.Where("a.section_uuid =?", in.SectionUuid)
		}
		t, e := session.Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).
			OrderBy("a.message_time asc,a.id asc").FindAndCount(&messages)
		if e != nil {
			log.Error(logPrefix, "获取会话消息列表失败:", err.Error())
			err = errors.New("获取会话消息列表失败")
			return
		}
		total = int(t)

		for _, v := range messages {
			if len(v.CloudFileUrl) > 0 {
				v.Content = v.CloudFileUrl
			}
		}
	}

	return
}

// 评价消息
func (s *CozeService) EvaluateMessage(in petai_vo.EvaluateMessageReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====评价消息,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	if in.Evaluate != petai_po.MessageEvaluateHate {
		in.Feedback = ""
		in.FeedbackType = 0
	}
	_, err = session.Table("eshop.petai_message").Alias("a").Cols("evaluate,feedback,feedback_type,evaluate_time").
		Where("a.id =?", cast.ToInt(in.MessageId)).Where("a.user_info_id =?", in.UserInfoId).
		Update(map[string]interface{}{
			"evaluate":      in.Evaluate,
			"feedback":      in.Feedback,
			"feedback_type": in.FeedbackType,
			"evaluate_time": time.Now(),
		})
	if err != nil {
		log.Error(logPrefix, "评价消息失败:", err.Error())
		err = errors.New("评价消息失败")
		return
	}

	return
}

func (s *CozeService) EvaluateConversation(in petai_vo.EvaluateConversationReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====互联网医生评价AI会话,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	_, err = session.Table("eshop.petai_conversation_transfer").Alias("a").Cols("evaluate,evaluate_time,feedback,feedback_type,feedback_doctor_name,feedback_doctor_code").
		Where("a.pm_order_sn =?", in.PmOrderSn).
		Update(map[string]interface{}{
			"evaluate":             in.Evaluate,
			"feedback":             in.Feedback,
			"feedback_type":        in.FeedbackType,
			"evaluate_time":        time.Now(),
			"feedback_doctor_name": in.FeedbackDoctorName,
			"feedback_doctor_code": in.FeedbackDoctorCode,
		})
	if err != nil {
		log.Error(logPrefix, "评价会话失败:", err.Error())
		err = errors.New("评价会话失败")
		return
	}

	return
}

// 获取会话列表
func (s *CozeService) ConversationList(in petai_vo.ConversationListReq) (conversations []*petai_po.PetaiConversationExd, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====获取会话列表====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.PageSize == 0 {
		in.PageSize = 100
	}

	conversations = make([]*petai_po.PetaiConversationExd, 0)

	session.Select("a.*").Table("eshop.petai_conversation").Alias("a").
		Join("inner", "eshop.user_info b", "a.user_info_id=b.user_info_id").
		Omit("a.bot_id")
	if len(in.UserInfoId) > 0 {
		session.Where("a.user_info_id =?", in.UserInfoId)
	}
	session.Where("is_deleted = 0") //查询未被删除的对话
	if len(in.Query) > 0 {
		session.Where("a.id=? or a.pet_name like ?  or a.title like ? or b.encrypt_user_mobile =?", cast.ToInt(in.Query), "%"+in.Query+"%", "%"+in.Query+"%", utils.MobileEncrypt(in.Query))
	}
	// if len(in.UserMobile) > 0 {
	// 	session.Where("b.encrypt_user_mobile =?", utils.MobileEncrypt(in.UserMobile))
	// }
	if len(in.PetInfoId) > 0 {
		session.Where("a.pet_info_id =?", in.PetInfoId)
	}
	// if len(in.PetName) > 0 {
	// 	session.Where("a.pet_name like ?", "%"+in.PetName+"%")
	// }
	if in.PetKindof != 0 {
		session.Where("a.pet_kindof =?", in.PetKindof)
	}
	if in.BotType > 0 {

		session.Where("a.bot_type =?", in.BotType)
	}
	// if len(in.CozeConversationId) > 0 {
	// 	session.Where("a.coze_conversation_id =?", in.CozeConversationId)
	// }
	// if len(in.Title) > 0 {
	// 	session.Where("a.title like ?", "%"+in.Title+"%")
	// }
	if len(in.CreateTime) > 0 {
		session.Where("a.create_time >=?", in.CreateTime[0]).Where("a.create_time <=?", in.CreateTime[1])
	}

	if len(in.UpdateTime) > 0 {
		session.Where("a.update_time >=?", in.UpdateTime[0]).Where("a.update_time <=?", in.UpdateTime[1])
	}

	orderBy := "a.update_time desc"
	if in.OrderBy == 1 {
		orderBy = "a.create_time desc"
	}
	t, e := session.Select("a.*,b.user_mobile").Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).
		OrderBy(orderBy).FindAndCount(&conversations)
	if e != nil {
		log.Error(logPrefix, "获取会话列表失败:", e.Error())
		err = errors.New("获取会话列表失败")
		return
	}
	total = int(t)

	return
}

// 转接互联网医生人工服务
func (s *CozeService) TransferPetMedical(in petai_vo.TransferPetMedicalReq) (isShow bool, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====转接互联网医生人工服务,入参:%s", utils.JsonEncode(in))
	// 查看该会话是否关联过宠物
	petaiConversationInfo, err := new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话信息失败:", err.Error())
		err = errors.New("获取会话信息失败")
		return
	}
	if petaiConversationInfo.Id == 0 {
		err = errors.New("会话信息不存在")
		return
	}

	// 判断是否已经关联过宠物
	if len(petaiConversationInfo.PetInfoId) == 0 {
		return
	}

	// 获取会话消息列表
	messageList, err := new(petai_po.PetaiMessage).GetConversationMessages(session, petai_po.GetMessagesReq{
		ConversationId: in.ConversationId,
		OrderBy:        "a.create_time desc,a.id desc",
	})

	if err != nil {
		log.Error(logPrefix, "获取会话消息列表失败:", err.Error())
		err = errors.New("获取会话消息列表失败")
		return
	}
	if len(messageList) == 0 {
		return
	}

	// 判断最后一条消息是否为ai
	if len(messageList) > 0 && messageList[0].DataFrom != petai_po.DataFromAI {
		return
	}

	// 获取ai回答最新被踩的时间
	var LastestEvaluateTime time.Time
	// 获取最新一次的 互联网医生人工服务 时间
	var LastestConsultTime time.Time
	for _, v := range messageList {
		if v.PetaiMessage.Evaluate == petai_po.MessageEvaluateHate && v.PetaiMessage.DataFrom == petai_po.DataFromAI {
			LastestEvaluateTime = v.PetaiMessage.EvaluateTime
			break
		}

		if v.PetaiMessage.DataFrom == petai_po.DataFromPetMedical {
			LastestConsultTime = v.PetaiMessage.MessageTime
		}
	}

	// 如果之前有互联网医生人工服务， 则判断最新踩的时间是否在 互联网医生人工服务 时间之后
	if !LastestConsultTime.IsZero() && (LastestEvaluateTime.Before(LastestConsultTime) || LastestEvaluateTime.Equal(LastestConsultTime)) {
		return
	}

	// 如果之前没有互联网医生人工服务， 则判断最新踩的时间是否存在， 存在则转接
	if LastestEvaluateTime.IsZero() {
		return
	}

	isShow = true
	return

}
