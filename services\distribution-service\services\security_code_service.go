package services

import (
	blky_po "eShop/domain/blky-po"
	upetmart_po "eShop/domain/upetmart-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"
)

// SecurityCodeService 防伪码服务
type SecurityCodeService struct {
	common.BaseService
}

// 品牌类型常量
const (
	BrandTypeUnknown      = 0 // 未知品牌
	BrandTypeGuizu        = 1 // 贵族（深圳利都）
	BrandTypeChongErXiang = 2 // 宠儿香
	BrandTypeKexueMiao    = 3 // 科学喵
)

// VerifySecurityCode 验证防伪码
func (s *SecurityCodeService) VerifySecurityCode(req vo.SecurityCodeVerifyReq) (vo.SecurityCodeVerifyResp, error) {
	s.<PERSON>gin()
	defer s.Close()

	response := vo.SecurityCodeVerifyResp{
		IsValid: false,
		Message: "查询错误",
	}

	if len(req.Code) != 20 {
		if req.VerifyType == 1 {
			response.MsgState = 202
		} else {
			response.MsgState = 201
		}
		return response, errors.New("未能验证防伪信息")
	}

	// 获取品牌类型
	brandType, err := s.GetBrandTypeByCode(req.Code, req.OrgId)
	if err != nil {
		if err.Error() == "未能验证防伪信息" {
			if req.VerifyType == 1 {
				response.MsgState = 202
			} else {
				response.MsgState = 201
			}
		} else if err.Error() == "未找到商品备案信息" {
			response.MsgState = 203
		}
		return response, err
	}

	response.BrandType = brandType

	// 根据品牌类型设置公司名称
	var companyName string
	switch brandType {
	case BrandTypeGuizu:
		companyName = "深圳利都"
	default:
		companyName = "北京百林康源"
	}

	// 获取用户信息
	if req.MemberId == 0 && req.OpenId != "" {
		user := &upetmart_po.UserMember{}
		if _, err := user.GetByOpenId(s.Session, req.OpenId, req.OrgId); err != nil {
			log.Errorf("查询用户失败: %v", err)
		}
		req.MemberId = user.MemberId
		req.UserId = user.ScrmUserId
		req.Mobile = user.MemberMobile
	}

	// 通过open_id查询user_id为空的记录，如果有，则批量更新历史记录
	if req.OpenId != "" && req.UserId != "" {
		// 使用领域模型方法更新用户信息
		verifyRecord := &blky_po.VerifyRecord{}
		updatedCount, err := verifyRecord.UpdateUserInfoByOpenId(s.Engine, req.OpenId, req.UserId, req.Mobile)
		if err != nil {
			log.Errorf("更新用户信息失败: %v", err)
		} else if updatedCount > 0 {
			log.Infof("成功更新%d条记录的user_id为%s", updatedCount, req.UserId)
		}
	}

	VerifyEntry := ""
	if req.Source == 1 {
		VerifyEntry = "首页-扫一扫"
	} else if req.Source == 2 {
		VerifyEntry = "微信扫一扫"
	} else if req.Source == 3 {
		VerifyEntry = "个人中心查验真伪"
	}
	// 2. 记录查询记录
	verifyRecord := &blky_po.VerifyRecord{
		CompanyName:      companyName,
		Code:             req.Code,
		VerifyTime:       time.Now(),
		UserOpenid:       req.OpenId,
		UserId:           req.UserId,
		UserPhone:        utils.AddStar(req.Mobile),
		EncryptUserPhone: utils.MobileEncrypt(req.Mobile),
		VerifyIp:         req.ClientIP,
		Source:           req.Source,
		VerifyLocation:   req.Location,
		VerifyEntry:      VerifyEntry,
		VerifyType:       req.VerifyType,
	}

	// 3. 查询该防伪码的查询历史记录
	var records []blky_po.VerifyRecord
	err = s.Engine.Table(verifyRecord.TableName()).
		Where("code = ?", req.Code).
		OrderBy("verify_time ASC").
		Find(&records)
	if err != nil {
		log.Errorf("查询防伪码历史记录失败: %v", err)
		return response, err
	}

	// 4. 保存本次查询记录
	_, err = s.Engine.Insert(verifyRecord)
	if err != nil {
		log.Errorf("保存防伪码查询记录失败: %v", err)
		return response, err
	}

	// 5. 根据查询次数返回不同结果
	queryCount := len(records)
	response.IsValid = true
	response.VerifyCount = queryCount + 1 // 包括本次查询

	// 添加以前的查询时间记录
	ordinals := []string{"首次", "二次", "三次"} // 可以根据需要扩展
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	if response.VerifyCount > 3 {
		timeInfo := vo.VerifyTimeInfo{
			Name:       ordinals[0] + "查询时间",
			VerifyTime: records[0].VerifyTime.Format("2006-01-02 15:04:05"),
		}
		response.VerifyTimes = append(response.VerifyTimes, timeInfo)
	} else if response.VerifyCount == 1 {
		response.VerifyTimes = append(response.VerifyTimes, vo.VerifyTimeInfo{
			Name:       "首次查询时间",
			VerifyTime: currentTime,
		})
	} else {
		for i, record := range records {
			if i < queryCount { // 只记录前三次查询
				timeInfo := vo.VerifyTimeInfo{
					Name:       ordinals[i] + "查询时间",
					VerifyTime: record.VerifyTime.Format("2006-01-02 15:04:05"),
				}
				response.VerifyTimes = append(response.VerifyTimes, timeInfo)
			}
		}
		// 再添加当前记录
		timeInfo := vo.VerifyTimeInfo{
			Name:       ordinals[queryCount] + "查询时间",
			VerifyTime: currentTime,
		}
		response.VerifyTimes = append(response.VerifyTimes, timeInfo)
	}

	// 根据查询次数返回不同信息
	if queryCount == 0 {
		// 首次查询
		response.Message = "正品"
		response.IsFirstQuery = true
	} else if queryCount > 0 && queryCount <= 2 {
		// 第2-3次查询
		response.Message = "正品"
		response.IsFirstQuery = false
	} else {
		// 超过3次查询
		response.Message = "正品"
		response.IsFirstQuery = false
		response.Warning = fmt.Sprintf("总扫码次数%d次，请确认是否本人所为", response.VerifyCount)
	}

	return response, nil
}

// GetBrandTypeByCode 根据防伪码获取品牌类型
func (s *SecurityCodeService) GetBrandTypeByCode(code string, orgId int) (int, error) {
	s.Begin()
	defer s.Close()

	// 1. 判断防伪码前4位是否为"9082"
	if code[0:4] == "9082" || code[0:4] == "4007" {
		// 深圳利都
		securityCode := &blky_po.XSecurityCode{}
		result, err := securityCode.GetByCode(s.Session, code)
		if err != nil {
			return BrandTypeUnknown, err
		}
		if result == nil {
			return BrandTypeUnknown, errors.New("未能验证防伪信息")
		}
		return BrandTypeGuizu, nil
	}

	// 2. 其他是百林康源，需要进一步判断具体品牌
	kucun := &blky_po.Xkucun{}
	result, err := kucun.GetByFwm(s.Session, code)
	if err != nil {
		return BrandTypeUnknown, err
	}
	if result == nil {
		return BrandTypeUnknown, errors.New("未能验证防伪信息")
	}

	// 3. 根据订单编号和商品名称查询具体品牌
	var gcId2 int
	sql := `SELECT g.gc_id_2 
			FROM upetmart.upet_goods g 
			LEFT JOIN blky.xorder_detail xd ON g.goods_serial = xd.scpbh 
			LEFT JOIN blky.xkucun x ON xd.sddbh = x.sddbh AND xd.sspmc = x.sspmc 
			WHERE x.sfwm = ? AND g.store_id = ? `

	has, err := s.Engine.SQL(sql, code, orgId).Get(&gcId2)
	if err != nil {
		return BrandTypeUnknown, err
	}
	if !has || gcId2 == 0 {
		return BrandTypeUnknown, errors.New("未找到商品备案信息")
	}

	// 4. 根据gc_id_2判断品牌
	if utils.IsProEnv() {
		// 生产环境判断
		switch gcId2 {
		case 1251:
			return BrandTypeChongErXiang, nil // 宠儿香
		case 1243:
			return BrandTypeKexueMiao, nil // 科学喵
		default:
			return BrandTypeUnknown, errors.New("未找到商品备案信息")
		}
	} else {
		// 测试环境判断
		switch gcId2 {
		case 1255:
			return BrandTypeChongErXiang, nil // 宠儿香
		case 1246:
			return BrandTypeKexueMiao, nil // 科学喵
		default:
			return BrandTypeUnknown, errors.New("未找到商品备案信息")
		}
	}
}

// UpdateUserInfo 更新用户信息
func (s *SecurityCodeService) UpdateUserInfo(req vo.UpdateUserInfoReq) error {
	s.Begin()
	defer s.Close()

	// 使用领域模型方法更新用户信息
	verifyRecord := &blky_po.VerifyRecord{}
	_, err := verifyRecord.UpdateUserInfoByOpenId(s.Engine, req.OpenId, req.UserId, req.Mobile)
	if err != nil {
		log.Errorf("更新用户信息失败: %v", err)
		return err
	}

	return nil
}
