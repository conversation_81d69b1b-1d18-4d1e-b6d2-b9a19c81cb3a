package services

import (
	"context"
	po "eShop/domain/marketing-po"
	omnibus_po2 "eShop/domain/omnibus-po"
	"eShop/infra/cache"
	"eShop/infra/converter"
	"eShop/infra/enum"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/transaction"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/marketing-vo"
	common_errors "errors"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/coze-dev/coze-go"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

// petArtworkService 作品服务实现
type PetArtworkService struct {
	common.BaseService
	tm transaction.TransactionManager
}

func NewPetArtworkService() PetArtworkService {
	return PetArtworkService{
		tm: transaction.NewTransactionManager(),
	}
}

// 生成极宠家 作品编号
func GeneratePetArtworkCode() string {
	logPrefix := "生成极宠家 作品编号===="
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	petArtworkCodeSli := redisConn.Get(string(cache_source.EShop), "petArtworkCode")
	log.Info(logPrefix, "petArtworkCodeSli:", petArtworkCodeSli)
	if len(petArtworkCodeSli[0].(string)) == 0 {
		petArtworkCode := fmt.Sprintf("GZ%06d", 1)
		redisConn.Save(string(cache_source.EShop), "petArtworkCode", petArtworkCode, 0)
		return petArtworkCode
	} else {
		petArtworkCode := petArtworkCodeSli[0].(string)
		tmp := strings.TrimPrefix(petArtworkCode, "GZ")
		petArtworkCodeInt, err := strconv.ParseInt(tmp, 10, 64) // 十进制，64位整数
		if err != nil {
			log.Error(logPrefix, "petArtworkCodeInt转换失败，err=", err.Error())
			return ""
		}
		petArtworkCode = fmt.Sprintf("GZ%06d", petArtworkCodeInt+1)
		redisConn.Save(string(cache_source.EShop), "petArtworkCode", petArtworkCode, 0)
		log.Info(logPrefix, "petArtworkCode:", petArtworkCode)
		return petArtworkCode
	}
}

// todo 获取随机3张参考图片
func GetRandom3RefImg() (string, string, string) {
	// 设置随机种子确保每次运行结果不同
	rand.Seed(time.Now().UnixNano())

	RefImgSli := []string{
		"https://file.vetscloud.com/646e586916c70b91afb180f10bb2ffa0.png",
		"https://file.vetscloud.com/1c5f928886b0242f43abc54e5933ae13.png",
		"https://file.vetscloud.com/269f41c446020bcd2e5b9ee624edbe1c.png",
		"https://file.vetscloud.com/e797da99de0d84451555abcb069c21fb.png",
		"https://file.vetscloud.com/ac9274478861ff3c795ae5761b8c11b7.png",
		"https://file.vetscloud.com/ac9274478861ff3c795ae5761b8c11b7.png",
		"https://file.vetscloud.com/1b6ab0157910e001783ed14eb8ea6150.png",
		"https://file.vetscloud.com/e7b62d79ab2d07fcf661811878d3959d.png",
		"https://file.vetscloud.com/ec7da5ccdfbb6211d25c8130421fa031.png",
		"https://file.vetscloud.com/27e05202ade5d97aac7c2ea5d327d939.png",
		"https://file.vetscloud.com/2fc2ac81696f179eedf09b7d776e14c2.png",
		"https://file.vetscloud.com/2fc2ac81696f179eedf09b7d776e14c2.png",
	}

	// 随机打乱切片
	rand.Shuffle(len(RefImgSli), func(i, j int) {
		RefImgSli[i], RefImgSli[j] = RefImgSli[j], RefImgSli[i]
	})

	// 取前3个元素
	selected := RefImgSli[:3]
	return selected[0], selected[1], selected[2]
}

// Create 创建作品
func (s PetArtworkService) Create(cmd vo.PetArtworkCreateReq) (out *po.PetArtwork, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("贵族裂变活动-创作作品,scrmUserId:%s====", cmd.ScrmUserId)
	log.Info(logPrefix, "入参", utils.JsonEncode(cmd))
	// 判断活动是否已结束
	petActivityInfo, err := new(PetActivityService).GetActivityInfo(session)
	if err != nil {
		log.Errorf("%s 获取活动信息失败%s", logPrefix, err.Error())
		err = errors.New("获取活动信息失败")
		return
	}
	if petActivityInfo.State == 2 {
		err = errors.New("活动已结束")
		return
	}

	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 加一个分布式锁， 避免用户连续重复点击
	lockKey := fmt.Sprintf("lock:pet_artwork_%s", cmd.ScrmUserId)
	lock, err := redisConn.SetNX2(string(cache_source.EShop), lockKey, 1, 10*time.Minute)
	defer redisConn.Delete(string(cache_source.EShop), lockKey)
	if err != nil {
		err = errors.New("系统繁忙，请稍后再试")
		log.Error(logPrefix, "设置redis锁失败,err=", err.Error())
		return nil, err
	} else if !lock {
		return nil, errors.New("您已有作品正在创作中,请勿频繁操作")
	}

	petrtworkList, hasPk, err := new(po.PetArtwork).FindByUserId(session, cmd.ScrmUserId)
	if err != nil {
		log.Error(logPrefix, "查询作品失败，err=", err.Error())
		err = errors.New("服务内部异常")
		return
	}
	if hasPk {
		err = errors.New("您已有参与PK的作品，不能再次创建作品")
		return
	}

	if len(petrtworkList) >= petActivityInfo.MaxImageNum {
		err = errors.New(fmt.Sprintf("生成次数已达上限：%d次，无法再生成新的图片", petActivityInfo.MaxImageNum))
		return
	}

	refImg1, refImg2, refImg3 := GetRandom3RefImg() //获取随机3张参考图片
	out = new(po.PetArtwork)

	out, err = s.Chat(vo.GuizuPetCozeChatReq{
		UserInfoId: cmd.ScrmUserId,
		BotType:    common.GuizuPetBloodCozeBotId,
		PetName:    cmd.PetName,
		PetPhoto:   cmd.PetPhoto,
		PetGender:  cmd.PetGender,
		RefImg1:    refImg1,
		RefImg2:    refImg2,
		RefImg3:    refImg3,
	})
	if err != nil {
		log.Error(logPrefix, "获取coze对话结果失败，err=", err.Error())
		return
	}

	petrtwork := new(po.PetArtwork)
	petrtwork.ScrmUserId = cmd.ScrmUserId
	petrtwork.WorkCode = GeneratePetArtworkCode() //生成作品编号
	petrtwork.PetName = cmd.PetName
	petrtwork.PetGender = cmd.PetGender
	petrtwork.PetPhoto = cmd.PetPhoto
	petrtwork.RefImg1 = refImg1
	petrtwork.RefImg2 = refImg2
	petrtwork.RefImg3 = refImg3
	petrtwork.NoblePetImg = out.NoblePetImg
	petrtwork.PetBreed = out.PetBreed
	petrtwork.BloodOrigin = out.BloodOrigin
	petrtwork.BloodRatio = out.BloodRatio
	petrtwork.GeneAttribute = out.GeneAttribute
	petrtwork.NobleTitleAward = out.NobleTitleAward
	petrtwork.NobleDescription = out.NobleDescription
	petrtwork.NobleScore = out.NobleScore
	petrtwork.NobleScoreDescription = out.NobleScoreDescription

	// 创建作品
	session.Begin()
	err = new(po.PetArtwork).Create(session, petrtwork)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "创建作品失败，err=", err.Error())
		return
	}
	// 添加参赛用户信息, 如果是首次创作作品，则新增参赛用户信息
	petContestant, exist, err := new(po.PetContestant).GetByScrmId(session, cmd.ScrmUserId)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "查询参赛用户信息失败，err=", err.Error())
		return
	}
	if !exist {
		petContestant.ScrmUserId = cmd.ScrmUserId
		petContestant.NickName = cmd.ScrmUserName
		petContestant.Mobile = utils.AddStar(cmd.Mobile)
		petContestant.EnMobile = utils.MobileEncrypt(cmd.Mobile)
		petContestant.FirstWorkTime = time.Now()
		petContestant.WorkCount = len(petrtworkList) + 1
		if err = new(po.PetContestant).Create(session, &petContestant); err != nil {
			session.Rollback()
			log.Error(logPrefix, "新增参赛用户信息失败，err=", err.Error())
			return
		}

	} else {
		//编辑已生成作品数量
		petContestant.WorkCount = len(petrtworkList) + 1
		_, err = session.Table("eshop.pet_contestant").Cols("work_count").Where("scrm_user_id=?", cmd.ScrmUserId).Update(map[string]interface{}{
			"work_count": petContestant.WorkCount})
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新作品数失败", err.Error())
			return
		}
	}
	session.Commit()
	go func() {

		var e error
		//第一次创建作品，发放5元无门槛优惠券
		if len(petrtworkList) == 0 {
			petActivityService := PetActivityService{}
			voucherResult, e := petActivityService.ReceiveVoucherByType(cmd.ScrmUserId, 5, 3)
			if e != nil {
				log.Error(logPrefix, "发放5元无门槛优惠券失败,err=", e.Error())
			} else if voucherResult != nil && voucherResult.Code == 200 {
				// 优惠券领取成功，尝试解析返回的优惠券数据
				var voucherCode string
				// 解析优惠券码 - 使用更简洁的方式
				if voucherResult != nil && voucherResult.Code == 200 {
					if voucherDataSlice, ok := voucherResult.Datas.([]utils.VoucherExchangeData); ok && len(voucherDataSlice) > 0 {
						// 直接使用VoucherExchangeData的字段
						voucherCode = fmt.Sprintf("%v", voucherDataSlice[0].VoucherCode)
					}
				}

				// 添加到PetPrize表
				prize := po.PetPrize{
					UserId:        cmd.ScrmUserId,
					NickName:      cmd.ScrmUserName,
					Mobile:        utils.AddStar(cmd.Mobile),
					EnMobile:      utils.MobileEncrypt(cmd.Mobile),
					PrizeType:     3, // 创作奖
					WorkCode:      petrtwork.WorkCode,
					PrizeCount:    0,
					ReceiveStatus: 2, // 优惠券已发放
					PrizeContent:  "5元无门槛券",
					CouponCode:    voucherCode, // 保存优惠券ID
					ReceiveTime:   time.Now(),
					CreateTime:    time.Now(),
					UpdateTime:    time.Now(),
				}

				// 创建新的session用于插入奖品记录
				prizeSession := s.Engine.NewSession()
				defer prizeSession.Close()
				if _, e = prizeSession.Table(po.PetPrize{}.TableName()).Insert(&prize); e != nil {
					log.Error(logPrefix, "插入我的奖品-5元无门槛优惠券记录失败,err=", e.Error())
				} else {
					log.Info(logPrefix, "成功插入奖品记录，优惠券券码:", voucherCode)
				}

			}
		}

		// 插入异步任务
		asyncTask := omnibus_po2.TaskListAsync{
			CreateId:         cmd.ScrmUserId,
			CreateName:       cmd.ScrmUserName,
			TaskContent:      enum.SyncPetArtworkTaskContent,
			OperationFileUrl: utils.JsonEncode(petrtwork),
			KeyStr:           "",
			ExtendedData:     enum.SyncTaskContentMap[enum.SyncPetArtworkTaskContent],
		}

		if e = asyncTask.CreateAsyncTask2(session); e != nil {
			log.Error(logPrefix, "创建贵族裂变活动 - AI生成贵族宠物图任务(极宠家)异步任务失败，err=", e.Error())
		}

	}()
	out.Id = petrtwork.Id
	return out, nil

}

func (s PetArtworkService) Chat(in vo.GuizuPetCozeChatReq) (petrtwork *po.PetArtwork, err error) {
	logPrefix := fmt.Sprintf("贵族裂变活动-coze对话====%s,%s", in.UserInfoId, in.BotType)
	log.Info(logPrefix, "入参", utils.JsonEncode(in))
	//  贵族宠物图，宠物品种，血统起源，血统占比，基因属性，贵族气息描述，荣获贵族封号，贵族气质评分，气质评分的解释
	petrtwork = new(po.PetArtwork)
	isCozeBlood := true  // true 直接调用coze 生成血统； false则是写死的，不会调用coze接口
	isCozeImage := false //true 直接调用coze 生成图片；false则是写死的， 不会调用coze接口

	//  todo 贵族裂变活动 上线前删除
	if in.UserInfoId == "3928fb9aef33430ebcd022d996539c91" { // uat卓燕武
		isCozeImage = true
	}
	if in.UserInfoId == "4ece8449dd12471fa38dfa122d107f71" { // uat许成鹏
		isCozeImage = true
	}
	if in.UserInfoId == "b85b85c50bd240d58d4f27797fd266ef" { // uat章思
		isCozeImage = true
	}
	if in.UserInfoId == "bcbfdce4ccbd43649f8255234c30447f" {
		isCozeImage = true
	}
	// 改成生产环境 的scrmid
	if in.UserInfoId == "6309717acd1240eaa208b811eae28b32" { // zhuoyw
		isCozeImage = true
	}

	if in.BotType == common.GuizuPetImageCozeBotId {
		petrtwork.NoblePetImg = "https://s.coze.cn/t/okh5bCOg8PM/"
	} else {
		petrtwork.PetBreed = "test拉布拉多"
		petrtwork.BloodOrigin = "美国加州贵族猫裔"
		petrtwork.BloodRatio = "检测到「95% 美国加州贵族猫裔」血脉"
		petrtwork.GeneAttribute = "亲和力 + 80%，优雅度 + 75%"
		petrtwork.NobleTitleAward = "星辰柔语・月纱领主"
		petrtwork.NobleDescription = "这只布偶猫浑身散发着优雅高贵的气息，蓬松柔软的毛发如云朵般洁白，脸部的灰色花纹好似精心勾勒的妆容，衬托出它的与众不同。湛蓝如宝石的双眼澄澈明净，仰头凝视的姿态尽显灵动与温婉，仿佛一位不食人间烟火的贵族淑女，静静地散发着温柔而典雅的魅力，每一处细节都彰显着布偶猫特有的甜美与高贵，让人忍不住为之倾倒。"
		petrtwork.NobleScore = cast.ToInt(940)
		petrtwork.NobleScoreDescription = "宠物神情（350）：蓝眼清澈灵动，表情温柔恬静，尽显优雅神态；宠物身姿（250）：坐姿挺拔端正，体态优美，展现出良好的优雅姿态；毛发光泽（200）：毛发蓬松顺滑，富有光泽，质感极佳；宠物名字（90）：“小布是只喵”简洁可爱，富有特色；衣着装扮（50）：无复杂配饰，保持自然状态，尽显猫咪本身的纯净之美。"
	}
	//  todo 贵族裂变活动 上线前删除

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()
	cozeService := common.NewCozeService()
	cozeCli, _ := cozeService.NewCozeAPI(in.UserInfoId, common.GuizuPetCozeType, in.BotType)
	stream := true
	autoSaveHistory := false
	message := make([]*coze.Message, 0)
	content := make([]*coze.MessageObjectString, 0)
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) {
		if in.PetName == "" {
			err = errors.New("宠物名称不能为空")
			return
		}
		if in.PetPhoto == "" {
			err = errors.New("宠物照片不能为空")
			return
		}
		petGender := "未知"
		if in.PetGender == 1 {
			petGender = "公"
		} else if in.PetGender == 2 {
			petGender = "母"
		}

		content = append(content, coze.NewTextMessageObject(fmt.Sprintf("宠物名称：%s", in.PetName)))
		content = append(content, coze.NewTextMessageObject(fmt.Sprintf("宠物性别：%s", petGender)))
		content = append(content, coze.NewImageMessageObjectByURL(in.PetPhoto))
		message = append(message, coze.BuildUserQuestionObjects(content, nil))

	} else if in.BotType == common.GuizuPetImageCozeBotId {
		if in.PetPhoto == "" || in.RefImg1 == "" || in.RefImg2 == "" || in.RefImg3 == "" {
			err = errors.New("宠物照片、参考图不能为空")
			return
		}

		content = append(content, coze.NewImageMessageObjectByURL(in.PetPhoto))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg1))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg2))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg3))
		message = append(message, coze.BuildUserQuestionObjects(content, nil))

	} else {
		err = errors.New("入参错误")
		return
	}

	req := &coze.CreateChatsReq{
		ConversationID:  "",
		BotID:           cozeService.GetBotId(in.BotType),
		UserID:          in.UserInfoId,
		Stream:          &stream,
		AutoSaveHistory: &autoSaveHistory,
		Messages:        message,
	}
	chatMessage := new(coze.Message)
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) && !isCozeBlood {
		log.Info(logPrefix, "血统是写死的")
		time.Sleep(20 * time.Second)
		return
	}
	if in.BotType == cast.ToString(common.GuizuPetImageCozeBotId) && !isCozeImage {
		log.Info(logPrefix, "生成图片是写死的")
		time.Sleep(40 * time.Second)
		return
	}

	resp, err := cozeCli.Chat.Stream(ctx, req)
	if err != nil {
		if in.BotType == common.GuizuPetImageCozeBotId {
			err = errors.New("需要再次放入队列处理|||" + err.Error())

		}
		log.Error(logPrefix, "对话失败,错误信息为", err.Error())
		return
	}
	defer resp.Close()

	for {
		event, e := resp.Recv()
		if common_errors.Is(e, io.EOF) {
			log.Info(logPrefix, "Stream finished")
			fmt.Println("Stream finished")
			break
		}
		if e != nil {
			log.Error(logPrefix, "接收数据发生错误:", e.Error())
			fmt.Println("接收数据发生错误:", e.Error())
			if in.BotType == common.GuizuPetImageCozeBotId {
				err = errors.New("需要再次放入队列处理|||接收数据发生错误" + e.Error())
			}

			return
		}

		//fmt.Printf("event:%s\n", utils.JsonEncode(event))
		if event.Event == coze.ChatEventConversationMessageCompleted { //完整对话消息
			if event.Message.Type == coze.MessageTypeAnswer {
				//消息返回的内容
				chatMessage = event.Message
			}
		}

	}

	log.Info(logPrefix, "|chatMessage", utils.JsonEncode(chatMessage))
	fmt.Println(logPrefix, "|chatMessage", utils.JsonEncode(chatMessage))
	// 贵族裂变活动 - coze智能体 宠物贵族血统鉴定师 解析结果
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) {
		if chatMessage.ContentType != coze.MessageContentTypeText {
			err = errors.New("coze智能体返回消息类型错误")
			return
		}

		petrtwork.PetBreed = utils.ExtractValue(chatMessage.Content, "宠物品种：", "\n")
		if petrtwork.PetBreed == "" {
			petrtwork.PetBreed = utils.ExtractBetween(chatMessage.Content, "宠物品种\n", "\n")
		}
		if petrtwork.PetBreed == "" {
			petrtwork.PetBreed = utils.ExtractBetween(chatMessage.Content, "宠物品种  \n", "\n")
		}
		petrtwork.BloodOrigin = utils.ExtractValue(chatMessage.Content, "血统起源：", "\n")
		petrtwork.BloodRatio = utils.ExtractValue(chatMessage.Content, "血统占比：", "\n")
		petrtwork.GeneAttribute = utils.ExtractValue(chatMessage.Content, "基因属性：", "\n")
		petrtwork.NobleTitleAward = utils.ExtractBetween(chatMessage.Content, "贵族封号：[", "]")
		petrtwork.NobleDescription = utils.ExtractValue(chatMessage.Content, "贵族气息描述：", "\n")
		if petrtwork.NobleDescription == "" {
			petrtwork.NobleDescription = utils.ExtractBetween(chatMessage.Content, "贵族气息描述\n", "\n")
		}
		if petrtwork.NobleDescription == "" {
			petrtwork.NobleDescription = utils.ExtractBetween(chatMessage.Content, "贵族气息描述  \n", "\n")
		}
		petrtwork.NobleScore = cast.ToInt(utils.ExtractValue(chatMessage.Content, "贵族气质评分：", "\n"))
		petrtwork.NobleScoreDescription = utils.ExtractValue(chatMessage.Content, "评分理由：", "\n")

		if petrtwork.PetBreed == "" && petrtwork.BloodOrigin == "" && petrtwork.BloodRatio == "" && petrtwork.GeneAttribute == "" && petrtwork.NobleTitleAward == "" && petrtwork.NobleDescription == "" && petrtwork.NobleScore == 0 && petrtwork.NobleScoreDescription == "" {
			log.Error(logPrefix, "coze获取血统失败", chatMessage.Content)
			err = errors.New(chatMessage.Content)
			return
		}
	} else if in.BotType == common.GuizuPetImageCozeBotId {
		// 查找https开头的部分
		start := strings.Index(chatMessage.Content, "https")
		if start == -1 {
			err = errors.New(chatMessage.Content)
			return
		}
		var ossUrl string
		// 提取https开头的完整URL  将coze图片地址保存到本地
		petrtwork.NoblePetImg = strings.TrimSpace(chatMessage.Content[start:])
		ossUrl, err = utils.UpImageToOss(petrtwork.NoblePetImg)
		if err != nil {
			log.Error(logPrefix, "上传图片到oss失败,err=", err.Error())
			// 上传图片上传两次
			ossUrl, err = utils.UpImageToOss(petrtwork.NoblePetImg)
			if err != nil {
				log.Error(logPrefix, "上传图片到oss再次失败,err=", err.Error())
				return
			} else {
				petrtwork.NoblePetImg = ossUrl
			}
		} else {
			petrtwork.NoblePetImg = ossUrl
		}
	} else {
		err = errors.New("入参错误")
		return
	}

	return
}

func (s PetArtworkService) ArtworkTest(in vo.PetArtworkTestReq) (err error) {
	petrtwork := new(po.PetArtwork)
	str := "宠物品种\n美国短毛猫\n\n宠物属性\n血统起源：北美本土猫裔\n血统占比：检测到「85% 北美本土猫裔」血脉\n基因属性：适应力基因 + 80%，捕猎本能 + 70%\n贵族封号：[森林猎手・斑影男爵]\n\n贵族气息描述\n它的贵族气息源于北美本土的捕猎传统，短而浓密的虎斑纹被毛如森林中的天然迷彩，每一道纹路都透着自然的野性与优雅。直立的耳朵警觉地朝向声源，大而明亮的眼睛里既有捕猎时的专注锐利，又带着对未知事物的好奇灵动。蜷缩的身姿恰似准备出击的猎手，那份灵动与沉稳的平衡，正是美国短毛猫独有的「森林贵族」风范——既保留了原始的捕猎本能，又不失家猫的温柔气质。\n\n贵族评分\n贵族气质评分：800\n评分理由：宠物神情（300）：眼睛明亮、面部放松，符合\"灵动\"特征；宠物身姿（220）：蜷缩姿态优雅，展现自然的捕猎准备状态；毛发光泽（180）：短而浓密，虎斑纹清晰有光泽；宠物名字（80）：\"喵喵咪呀你\"可爱但稍显冗长；衣着装扮（20）：未佩戴任何配饰。"

	// 未识别到品种 和 贵族气息
	str = "### 宠物品种  \n柴犬  \n\n### 宠物属性  \n血统起源：日本原生犬裔  \n血统占比：检测到「95% 日本原生犬裔」血脉  \n基因属性：警惕基因 + 65%，环境适应力 + 70%  \n贵族封号：[日本原生犬・赤焰男爵]  \n\n### 贵族气息描述  \n它的贵族气息源自日本原生犬的古老血统，赤棕色短毛如被阳光晒暖的绸缎，白色胸毛恰似坠在胸前的玉佩。直立的耳朵像两簇警觉的火苗，卷成毛球的尾巴微微翘起，仿佛握着一把象征身份的小权杖。嘴角轻扬的憨笑带着原生犬的质朴，眼神里既有对周围环境的警惕，又含着对主人的温柔，每一步都走得从容不迫，像从日本乡村贵族宅邸里走出的小绅士。  \n\n### 贵族评分  \n贵族气质评分：730  \n评分理由：宠物神情（180）：嘴角上扬、吐舌，符合 \"憨笑\" 特征；宠物身姿（250）：身体主干垂直、前肢伸直、姿态庄重，符合 \"端坐\" 特征；毛发光泽（200）：被毛短而光滑，有明显质感；宠物名字（80）：\"哈哈哈\" 简洁易记；衣着装扮（20）：无任何衣物或配饰，仅自然状态。"
	str = "宠物品种  \n中华田园猫  \n\n宠物属性  \n血统起源：中国本土家猫裔  \n血统占比：检测到「85% 中国本土家猫裔」血脉  \n基因属性：适应力基因 + 80%，捕猎本能 + 70%  \n贵族封号：[林间猎手・虎纹男爵]  \n\n贵族气息描述  \n它的贵族气息源于中国本土家猫的古老血统，虎斑纹被毛如揉碎的阳光洒在林间，透着自然的野趣与灵动。直立的耳朵像两座小巧的雷达，警觉却不尖锐；绿色眼睛似浸在茶里的翡翠，既有捕猎者的机敏，又含家猫特有的温顺。端坐于橙色椅子上的姿态，前肢伸直、后肢微曲，恰似一位守护领地的小男爵——内敛的气场里藏着对生活的敏锐感知，这正是中华田园猫独有的「自然贵气」。  \n\n贵族评分  \n贵族气质评分：820  \n评分理由：宠物神情（300）：眼神柔和，面部肌肉放松，符合\"温柔\"特征；宠物身姿（250）：身体主干垂直，前肢伸直、后肢弯曲，符合\"端坐\"的庄重姿态；毛发光泽（180）：被毛顺滑有层次，虎斑纹清晰；宠物名字（70）：\"大橘子\"为食物名，属于\"可爱名\"分类；衣着装扮（20）：无额外配饰，仅自然状态。"
	str = "### 宠物品种  \n美国短毛猫（虎斑色）  \n\n### 宠物属性  \n血统起源：北美本土培育猫裔  \n血统占比：检测到「95% 北美本土培育猫裔」血脉  \n基因属性：适应性基因 + 80%，捕猎本能 + 75%  \n贵族封号：[阳光斑纹・金缕伯爵夫人]  \n\n### 贵族气息描述  \n它的贵族气息源于北美本土培育的扎实血统，短而浓密的虎斑纹被毛如金缕织就，泛着健康的光泽。直立的耳朵警觉前倾，大而明亮的眼睛里透着好奇与灵动，前爪抬起的姿态恰似准备探索的小绅士，既保留了捕猎本能的敏锐，又带着家庭伴侣的温柔，那份自然流露的活泼与自信，正是美短独有的「阳光贵族」气质。  \n\n### 贵族评分  \n贵族气质评分：810  \n评分理由：宠物神情（300）：眼睛明亮、面部表情好奇，符合 \"灵动\" 特征；宠物身姿（220）：前爪抬起、姿态活泼，展现敏捷体态；毛发光泽（180）：短毛浓密、虎斑纹清晰有光泽；宠物名字（90）：\"小樽\" 简洁可爱，符合家庭伴侣的亲切气质；衣着装扮（20）：未佩戴任何衣物或配饰，保持自然状态。"

	// 新的
	str = "一、品种  \n宠物品种：中华田园猫  \n\n二、宠物属性  \n血统起源：中国本土家猫裔  \n血统占比：检测到「85% 中国本土家猫裔」血脉  \n基因属性：适应力基因 + 80%，捕猎本能 + 70%  \n贵族封号：[乡野猎手・黄纹校尉]  \n\n三、贵族气质  \n贵族气息描述：它的贵族气息源于中国本土家猫的古老血统，短而浓密的虎斑纹被毛如乡土间的虎皮，透着自然的野性与灵动。竖立的耳朵警觉前倾，绿色眼眸中既有捕猎时的敏锐，又含家猫的温顺。端坐于橙色椅子上的姿态，恰似乡野间的小猎手，内敛却难掩骨子里的活泼与坚韧，那份自然流露的灵动，正是中华田园猫独有的「乡土贵族」气质。  \n\n四、贵族评分  \n贵族气质评分：940  \n评分理由：宠物神情（350）：眼神平静面部放松，符合 \"温柔\" 特征；宠物身姿（250）：端坐于椅子上，后肢弯曲前肢伸直，展现庄重体态；毛发光泽（200）：被毛浓密有质感；宠物名字（90）：\"靓仔\" 简洁亲切；衣着装扮（50）：无任何配饰，保持自然状态。"
	petrtwork.PetBreed = utils.ExtractValue(str, "宠物品种：", "\n")
	if petrtwork.PetBreed == "" {
		petrtwork.PetBreed = utils.ExtractBetween(str, "宠物品种  \n", "\n")
	}
	petrtwork.BloodOrigin = utils.ExtractValue(str, "血统起源：", "\n")
	petrtwork.BloodRatio = utils.ExtractValue(str, "血统占比：", "\n")
	petrtwork.GeneAttribute = utils.ExtractValue(str, "基因属性：", "\n")
	petrtwork.NobleTitleAward = utils.ExtractBetween(str, "贵族封号：[", "]")
	petrtwork.NobleDescription = utils.ExtractBetween(str, "贵族气息描述  \n", "\n")
	petrtwork.NobleScore = cast.ToInt(utils.ExtractValue(str, "贵族气质评分：", "\n"))
	petrtwork.NobleScoreDescription = utils.ExtractValue(str, "评分理由：", "\n")

	fmt.Println(petrtwork)
	return

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====贵族裂变活动-AI生成贵族宠物图任务,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	var rdb = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 5. 同步工具
	var (
		wg sync.WaitGroup
		//results = make(chan string, 100) // 按顺序收集结果
		//resultMutex sync.Mutex
	)

	// 模拟请求
	for i := 0; i < 30; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()

			// 使用原子操作获取下一个智能体
			var agentID string = "7526851960020992046"
			// agentID, err = utils.GetNextAgentAtomic(rdb)

			// if err != nil {
			// 	log.Errorf("Request %d failed to get agent: %v", requestID, err)
			// 	return
			// }
			// 安全地收集结果
			// resultMutex.Lock()
			// results <- fmt.Sprintf("请求 %d 分配到智能体: %s", requestID, agentID)
			// resultMutex.Unlock()
			if err := utils.CallCozeWithToken(rdb, requestID, agentID); err != nil {

			}
		}(i)
	}
	wg.Wait()
	//close(results)

	// 8. 按顺序打印结果
	// fmt.Println("==== 智能体分配结果 ====")
	// for result := range results {
	// 	fmt.Println(result)
	// }
	time.Sleep(4 * time.Minute)
	return

}

// Update 更新作品
func (s PetArtworkService) SelectedPk(session *xorm.Session, cmd vo.PetArtworkUpdateReq) error {
	return s.tm.Required(session, func(tx *xorm.Session) error {
		artwork, err := new(po.PetArtwork).GetByID(tx, cmd.Id)
		if err != nil {
			return err
		}
		if artwork.Id == 0 {
			return errors.NewBadRequest("作品不存在")
		}

		// 查询当前用户是否已经有参与PK的作品
		has, err := s.ExistPk(tx, artwork.ScrmUserId)
		if err != nil {
			return err
		}
		if has {
			return errors.NewBadRequest("您已有参与PK的作品，不能再次参与PK")
		}

		updEntity, err := converter.DeepConvert[po.PetArtwork](cmd)
		if err != nil {
			return err
		}

		updEntity.Id = artwork.Id
		err = new(po.PetArtwork).Update(tx, updEntity)
		if err != nil {
			return err
		}

		// 更新参赛用户表
		_, err = tx.Exec("UPDATE pet_contestant SET is_pk = ?, pk_work_id = ?, pk_time = ? WHERE scrm_user_id = ?", 1, artwork.WorkCode, utils.GetTimeNow(time.Now()), artwork.ScrmUserId)
		if err != nil {
			return err
		}

		return nil
	})
}

func (s PetArtworkService) ExistPk(session *xorm.Session, userId string) (bool, error) {
	var has bool
	err := s.tm.Required(session, func(tx *xorm.Session) error {
		var err error
		var id int
		has, err = tx.Table("pet_artwork").Select("id").Where("scrm_user_id = ? AND pk_status = ?", userId, 1).Get(&id)
		if err != nil {
			return errors.NewBadRequest("查询到用户是否已有参与PK的作品失败" + err.Error())
		}
		return nil
	})
	return has, err
}

// Page 作品分页查询
func (s PetArtworkService) Page(session *xorm.Session, query vo.PetArtworkPageReq) ([]vo.PetArtworkResp, int64, error) {
	var resps []vo.PetArtworkResp
	var total int64
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error

		MiniCondition := ""
		if query.IsMiniApp == 1 {
			MiniCondition = " WHERE pk_status = 1 "
		}

		sql := "SELECT pet_artwork.* " +
			"FROM ( " +
			"SELECT *, DENSE_RANK() OVER (ORDER BY vote_count DESC, pk_time ASC) AS vote_count_rank " +
			"FROM pet_artwork " + MiniCondition +
			") AS pet_artwork " +
			"LEFT JOIN pet_prize " +
			"    ON pet_prize.work_code = pet_artwork.work_code " +
			"    AND pet_prize.prize_type = 1 "

		conditions := utils.GetQueryCondition(query)
		if conditions != "" {
			sql += " WHERE " + conditions
		}

		if query.IsMiniApp == 1 {
			sql += " ORDER BY vote_count_rank asc"
		} else {
			sql += " ORDER BY pet_artwork.id DESC"
		}

		if query.PageIndex > 0 && query.PageSize > 0 {
			sql += fmt.Sprintf(" LIMIT %d OFFSET %d", query.PageSize, (query.PageIndex-1)*query.PageSize)
		}

		err = tx.SQL(sql).Find(&resps)
		if err != nil {
			return err
		}

		// 获取总数
		total, err = tx.Table("pet_artwork").Where(conditions).Count()
		if err != nil {
			return err
		}

		return nil
	})
	return resps, total, err
}

// Detail 作品详情查询
func (s PetArtworkService) Detail(session *xorm.Session, query vo.PetArtworkDetailReq) (vo.PetArtworkResp, error) {
	var resp vo.PetArtworkResp
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		sql := `SELECT pet_artwork.*
		FROM (
			SELECT *, DENSE_RANK() OVER (ORDER BY vote_count DESC, pk_time ASC) AS vote_count_rank FROM pet_artwork
		) AS pet_artwork`

		conditions := utils.GetQueryCondition(query)
		if conditions != "" {
			sql += " WHERE " + conditions
		}

		sql += " LIMIT 1"

		has, err := tx.SQL(sql).Get(&resp)
		if err != nil {
			return err
		}

		// 计算气质评分排名
		if has {
			nobleScore := resp.NobleScore

			// 获取宠物排名和总数
			scoreList := make([]int, 0)
			sql := `SELECT MAX(noble_score) AS noble_score 
			FROM pet_artwork 
			GROUP BY scrm_user_id, pet_name, pet_breed, pet_gender 
			ORDER BY noble_score DESC;
			`
			err := tx.SQL(sql).Find(&scoreList)
			if err != nil {
				return err
			}

			totalPets := len(scoreList)
			resp.PetCount = totalPets
			if len(scoreList) > 0 {
				rank := 1 // 默认排名为总数+1
				for i, score := range scoreList {
					if nobleScore <= score {
						rank = i + 1
					} else {
						break
					}
				}

				// 计算超过百分比
				percentage := (1 - float64(rank)/float64(totalPets)) * 100
				// 去掉小数部分，保留整数
				resp.NobleRankPercent = int(percentage)
			}
		}

		return nil
	})
	return resp, err
}
