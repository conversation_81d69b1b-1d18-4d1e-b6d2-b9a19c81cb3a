package services

import (
	petai_po "eShop/domain/petai-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	petai_vo "eShop/view-model/petai-vo"
	"errors"
	"fmt"
	"math"
	"net/http"
	"time"
)

// NewPetVaccinateService 创建宠物健康档案
func NewPetVaccinateService() *PetVaccinateService {
	return &PetVaccinateService{}
}

type PetVaccinateService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.JwtInfo
}

// 宠物健康档案新增或更新
func (s *PetVaccinateService) PetVaccinateAddOrUpdate(in petai_vo.PetVaccinate) (err error) {

	s.<PERSON>gin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====宠物健康档案新增或更新,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	OperationDate, err := time.ParseInLocation(utils.DateLayout, in.OperationDate, time.Local)
	if err != nil {
		log.Error(logPrefix+"====解析日期 err=", err.Error())
		return errors.New("解析日期失败")
	}
	if in.DataSource == 0 {
		in.DataSource = petai_po.DataSourceSelf
	}
	petVaccinate := petai_po.UserPetVaccinateRecord{
		PetInfoId:        in.PetInfoId,
		Type:             in.Type,
		OperationYear:    OperationDate.Year(),
		OperationDate:    OperationDate,
		ProductName:      in.ProductName,
		ProductCode:      in.ProductCode,
		ShopName:         in.ShopName,
		Number:           in.Number,
		NumberOf:         in.NumberOf,
		Category:         in.Category,
		RecordPhoto:      in.RecordPhoto,
		DataSource:       in.DataSource,
		TreatmentOutcome: in.TreatmentOutcome,
		AllergicReaction: in.AllergicReaction,
	}
	if in.Id <= 0 {
		if _, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&petVaccinate); err != nil {
			log.Error(logPrefix+" 新增记录失败 err=", err.Error())
			return errors.New("新增记录失败")
		}

	} else {
		_, err = session.Table("eshop.user_pet_vaccinate_record").
			Cols("pet_info_id,type,operation_year,operation_date,product_code,product_name,shop_name,number,number_of,category,record_photo,treatment_outcome").
			Where("id = ?", in.Id).Update(&petVaccinate)
		if err != nil {
			log.Error(logPrefix+"====更新记录失败 err=", err.Error())
			return errors.New("更新记录失败")
		}
	}

	return

}

// 获取用户宠物驱虫免疫记录
func (s *PetVaccinateService) PetVaccinateList(in petai_vo.PetVaccinateListReq) (out []*petai_vo.PetVaccinate, total int64, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("==== 获取用户宠物健康记录,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	sel := " a.id, a.pet_info_id , a.section_uuid,a.pet_id ,a.type,a.operation_year, a.operation_date ,a.shop_name ,a.product_code ,a.product_name,a.number,a.number_of,a.category,a.record_photo,a.third_no,a.data_source,a.delete_reason,a.treatment_outcome,a.allergic_reaction"
	out = make([]*petai_vo.PetVaccinate, 0)
	session.Table("eshop.user_pet_vaccinate_record").Alias("a").Select(sel)
	if in.Type > 0 {
		session.Where("a.type =?", in.Type)
	}
	if in.PetInfoId != "" {
		session.Where("a.pet_info_id =?", in.PetInfoId)
	}

	if in.ConversationId > 0 {
		session.Where("a.conversation_id =?", in.ConversationId)
	}
	if in.SectionUuid != "" {
		session.Where("a.section_uuid =?", in.SectionUuid)
	}
	total, err = session.Where(" is_delete = 0").OrderBy("a.operation_date desc ").Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).FindAndCount(&out)
	if err != nil {
		log.Error(logPrefix+"==== 查询数据失败 err=", err.Error())
		err = errors.New("查询数据失败")
		return
	}

	return

}

// 获取宠物所有过敏源
func (s *PetVaccinateService) GetPetAllergy(in petai_vo.PetVaccinateListReq) (allergyStr string, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("==== 获取用户宠物所有过敏源,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	AllergyList, _, err := s.PetVaccinateList(in)
	if err != nil {
		return
	}

	if len(AllergyList) > 0 {
		for _, v := range AllergyList {
			if len(v.ShopName) > 0 {
				if allergyStr != "" {
					allergyStr += ","
				}
				allergyStr += v.ShopName
			}

		}
	}

	return

}

// 删除健康记录
func (s *PetVaccinateService) PetVaccinateDelete(in petai_vo.PetVaccinateDeleteReq) (err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====宠物健康档案删除,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	petVaccinate := petai_po.UserPetVaccinateRecord{
		IsDelete:     1,
		DeleteReason: in.DeleteReason,
	}
	_, err = session.Table("eshop.user_pet_vaccinate_record").
		Cols("is_delete,delete_reason").
		Where("id = ?", in.Id).Update(&petVaccinate)
	if err != nil {
		log.Error(logPrefix+"====删除记录失败 err=", err.Error())
		return errors.New("删除记录失败")
	}

	return

}

func (s *PetVaccinateService) GetPetVaccinateLatestList(petInfoId string) (data []*petai_vo.PetRecordDate, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("==== 获取用户宠物最新的健康记录,入参:%s", petInfoId)
	log.Info(logPrefix)
	data = make([]*petai_vo.PetRecordDate, 0)
	// 查询自建记录的时间
	// sql := "select id, pet_info_id,pet_id, `type` record_type, operation_date, record_photo, product_name, create_time,number,category from eshop.user_pet_vaccinate_record " +
	// 	"as a where pet_info_id = ?  and is_delete=0 and not exists(select b.id from eshop.user_pet_vaccinate_record as b where b.pet_info_id = a.pet_info_id " +
	// 	"and b.type = a.type and b.create_time > a.create_time);"

	sql := `SELECT id, pet_info_id,pet_id, type record_type, operation_date, record_photo, product_name, create_time,number,category
FROM (
    SELECT 
        *,
        ROW_NUMBER() OVER (
            PARTITION BY pet_info_id, type 
            ORDER BY operation_date DESC, update_time DESC, id DESC
        ) AS rn
    FROM user_pet_vaccinate_record
    WHERE 
        pet_info_id = ?
        AND is_delete = 0
) AS ranked
WHERE rn = 1;`
	onlyData := make([]*petai_vo.PetRecordDate, 0)
	err = session.SQL(sql, petInfoId).Find(&onlyData)
	if err != nil {
		log.Error(logPrefix+"==== 查询健康记录的时间异常 err=", err.Error())
		err = errors.New("查询健康记录的时间异常")
		return
	}

	onlyDataMap := make(map[int64]*petai_vo.PetRecordDate)
	for i := range onlyData {
		onlyDataMap[onlyData[i].RecordType] = onlyData[i]
	}

	for _, v := range onlyDataMap {
		data = append(data, v)
	}

	return
}

// 查询宠物健康记录的时间
func (s *PetVaccinateService) GetNewPetDate(in petai_vo.GetNewPetDateReq) (out []*petai_vo.NewPetRecordDateRes, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("==== 查询宠物健康记录的时间,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	var onlyData []*petai_vo.PetRecordDate
	onlyData, err = s.GetPetVaccinateLatestList(in.PetInfoId)

	if err != nil {
		log.Error(logPrefix+"==== 查询健康记录的时间异常 err=", err.Error())
		err = errors.New("查询健康记录的时间异常")
		return
	}
	var neuteringData petai_vo.ShowPetNeuteringData
	if _, err = session.Table("eshop.user_pet_info").Select("pet_birthday,pet_neutering,pet_kindof").
		Where("pet_info_id =?", in.PetInfoId).
		Get(&neuteringData); err != nil {
		log.Error(logPrefix+"==== 查询宠物信息异常 err=", err.Error())
		err = errors.New("查询宠物信息异常")
		return
	}

	//记录存在的类型
	var hasTypeMap = make(map[int64]bool, len(onlyData))
	now := time.Now()
	out = make([]*petai_vo.NewPetRecordDateRes, 0)
	for i := range onlyData {
		date := onlyData[i]
		var resData = petai_vo.NewPetRecordDateRes{
			RecordPhoto:   date.RecordPhoto,
			ProductName:   date.ProductName,
			RecordType:    date.RecordType,
			CreateTime:    date.CreateTime,
			OperationDate: date.OperationDate,
		}
		hasTypeMap[date.RecordType] = true
		switch date.RecordType {
		//红点展示
		case 3, 4:
			//未记录 + 最后一次记录离现在超过365自然天；（和之前的逻辑一样）
			if len(resData.OperationDate) > 0 {
				location, _ := time.ParseInLocation(utils.DateLayout, resData.OperationDate, time.Local)
				add := location.AddDate(1, 0, 0)
				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindOneYearLater := add.AddDate(0, 0, -3)
				if now.After(remindOneYearLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}

		case 5:
			//洗护 猫咪建议3月1次、狗狗建议2周1次
			if len(resData.OperationDate) > 0 {
				location, _ := time.ParseInLocation(utils.DateLayout, resData.OperationDate, time.Local)
				switch neuteringData.PetKindof {
				case 1000:
					add := location.AddDate(0, 3, 0)
					if now.After(add) {
						resData.Show = true
					}
					remindThreeMonthsLater := add.AddDate(0, 0, -3)
					if now.After(remindThreeMonthsLater) && now.Before(add) {
						resData.Remind = true
					}
					hour := time.Until(add).Hours() / 24
					resData.DayNum = int32(math.Ceil(hour))
				case 1001:
					add := location.Add(14 * 24 * time.Hour)
					if now.After(add) {
						resData.Show = true
					}
					remindTwoWeeksLater := add.AddDate(0, 0, -3)
					if now.After(remindTwoWeeksLater) && now.Before(add) {
						resData.Remind = true
					}
					hour := time.Until(add).Hours() / 24
					resData.DayNum = int32(math.Ceil(hour))
				}
			} else {
				resData.Show = true
			}

		case 6, 7, 8:
			// 6体况评分 7三围 8体重：未记录 + 最后一次记录离现在超过一个月
			if len(resData.OperationDate) > 0 { // 未记录 + 最后一次记录离现在超过60自然天
				location, _ := time.ParseInLocation(utils.DateLayout, resData.OperationDate, time.Local)
				add := location.AddDate(0, 1, 0)
				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindThreeMonthsLater := add.AddDate(0, 0, -3)
				if now.After(remindThreeMonthsLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}
		case 1, 2:
			//免疫：最后一次记录离现在超过365自然天；
			//驱虫：最后一次记录离现在超过90自然天
			// 1疫苗 2驱虫：未记录 + 最后一次记录离现在超过90自然天
			if len(resData.OperationDate) > 0 {
				location, _ := time.ParseInLocation(utils.DateLayout, resData.OperationDate, time.Local)
				var add time.Time
				// 免疫
				if date.RecordType == 1 {
					add = location.AddDate(1, 0, 0)
				} else { // 驱虫
					add = location.AddDate(0, 3, 0)
				}

				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindOneYearLater := add.AddDate(0, 0, -3)
				if now.After(remindOneYearLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}
		case 101:
			resData.Show = false
		}

		out = append(out, &resData)
	}

	// 绝育展示
	var resDataMap = petai_vo.NewPetRecordDateRes{}

	birthdayTime, _ := time.Parse(utils.DateTimeLayout, neuteringData.PetBirthday)
	month, day, _ := GetAge(time.Now(), birthdayTime)
	if month > 12 && neuteringData.PetNeutering == 0 {
		resDataMap.Show = true
	}
	if month == 12 && day > 0 && neuteringData.PetNeutering == 0 {
		resDataMap.Show = true
	}

	out = append(out, &resDataMap)
	//没有记录的类型也返回
	for i := int64(1); i < 9; i++ {
		if !hasTypeMap[i] {
			out = append(out, &petai_vo.NewPetRecordDateRes{
				RecordType: i,
				Show:       true,
				Remind:     true,
			})
		}
	}

	if !hasTypeMap[petai_po.UserPetVaccinateRecordTypeHistory] {
		out = append(out, &petai_vo.NewPetRecordDateRes{
			RecordType: petai_po.UserPetVaccinateRecordTypeHistory,
			Show:       true,
			Remind:     true,
		})
	}

	// 宠物病史展示
	return

}
